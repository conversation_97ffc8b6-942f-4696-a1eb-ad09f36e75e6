# نافذة تقرير التدفقات النقدية (Cash Flow Report)
# تحتوي على: تتبع الأموال الداخلة والخارجة، التحليل الزمني، التوقعات

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. المخططات البيانية لن تعمل.")

class CashFlowManager:
    """مدير بيانات التدفقات النقدية"""

    def __init__(self, db_path="data.db"):
        self.db_path = db_path

    def create_main_account_table(self):
        """إنشاء جدول الحساب الرئيسي"""
        try:
            print("🔍 [DEBUG] إنشاء جدول الحساب الرئيسي...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف الجدول القديم إذا كان موجوداً
            cursor.execute("DROP TABLE IF EXISTS الحساب_الرئيسي")

            # إنشاء الجدول الجديد
            cursor.execute("""
                CREATE TABLE الحساب_الرئيسي (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    تاريخ_العملية DATE NOT NULL,
                    نوع_العملية TEXT NOT NULL,
                    بيان_العملية TEXT NOT NULL,
                    الدخول REAL DEFAULT 0,
                    الخروج REAL DEFAULT 0,
                    ملاحظات TEXT,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم إنشاء جدول الحساب الرئيسي بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء جدول الحساب الرئيسي: {str(e)}")
            return False
    


    def populate_main_account_table(self, start_date, end_date):
        """تجميع البيانات من الجداول الثلاثة وإدراجها في الجدول الرئيسي"""
        try:
            print(f"🔍 [DEBUG] تجميع البيانات من {start_date} إلى {end_date}")

            # إنشاء جدول الحساب الرئيسي
            if not self.create_main_account_table():
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 1. جلب الواجبات الشهرية - تجميع حسب created_date والقسم
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='monthly_duties'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول monthly_duties...")
                cursor.execute("""
                    SELECT DATE(created_date) as تاريخ_التجميع,
                           القسم,
                           month,
                           SUM(amount_paid) as إجمالي_المبلغ
                    FROM monthly_duties
                    WHERE DATE(created_date) BETWEEN ? AND ?
                    AND amount_paid > 0
                    GROUP BY DATE(created_date), القسم, month
                    ORDER BY DATE(created_date) DESC
                """, (start_date, end_date))

                monthly_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد مجموعات الواجبات الشهرية: {len(monthly_results)}")

                for row in monthly_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # تاريخ_التجميع (created_date)
                        'الواجبات الشهرية',
                        row[2] or 'واجب شهري',  # month (بيان العملية)
                        float(row[3]) if row[3] else 0.0,  # إجمالي_المبلغ (الدخول)
                        0,  # الخروج
                        row[1] or ''  # القسم (ملاحظات)
                    ))

            # 2. جلب رسوم التسجيل
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='registration_fees'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول registration_fees...")
                cursor.execute("""
                    SELECT payment_date, amount_paid, payment_type, notes
                    FROM registration_fees
                    WHERE payment_date BETWEEN ? AND ?
                    AND amount_paid > 0
                """, (start_date, end_date))

                registration_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد رسوم التسجيل: {len(registration_results)}")

                for row in registration_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # payment_date
                        'رسوم تسجيل',
                        f"رسوم تسجيل - {row[2]}",  # payment_type
                        float(row[1]) if row[1] else 0.0,  # amount_paid
                        0,
                        row[3] or ''  # notes
                    ))

            # 3. جلب المصاريف
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='المصاريف'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول المصاريف...")
                cursor.execute("""
                    SELECT التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة, ملاحظات
                    FROM المصاريف
                    WHERE التاريخ BETWEEN ? AND ?
                """, (start_date, end_date))

                expenses_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد المصاريف: {len(expenses_results)}")

                for row in expenses_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # التاريخ
                        row[2] or 'مصروف عام',  # نوع_المصروف
                        f"{row[2]} - {row[3]}" if row[3] else row[2],  # بيان مع الجهة المستفيدة
                        0,
                        float(row[1]) if row[1] else 0.0,  # المبلغ
                        row[4] or ''  # ملاحظات
                    ))

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم تجميع البيانات في الجدول الرئيسي بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تجميع البيانات: {str(e)}")
            return False

    def get_main_account_data(self, start_date, end_date):
        """جلب البيانات من جدول الحساب الرئيسي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT تاريخ_العملية, نوع_العملية, بيان_العملية,
                       الدخول, الخروج, ملاحظات
                FROM الحساب_الرئيسي
                WHERE تاريخ_العملية BETWEEN ? AND ?
                ORDER BY تاريخ_العملية DESC
            """, (start_date, end_date))

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    'date': row[0],
                    'type': row[1],
                    'description': row[2],
                    'inflow': float(row[3]) if row[3] else 0.0,
                    'outflow': float(row[4]) if row[4] else 0.0,
                    'notes': row[5] or ''
                }
                for row in results
            ]

        except Exception as e:
            print(f"❌ [ERROR] خطأ في جلب بيانات الحساب الرئيسي: {str(e)}")
            return []



class CashFlowWindow(QMainWindow):
    """نافذة تقرير التدفقات النقدية"""
    
    def __init__(self):
        super().__init__()
        self.manager = CashFlowManager()
        self.init_ui()
        self.load_cash_flow_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💸 تقرير التدفقات النقدية (Cash Flow)")
        self.setWindowState(Qt.WindowMaximized)  # فتح في كامل الشاشة مباشرة
        
        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #87ceeb;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # شريط العنوان مع الأزرار
        self.create_title_bar_with_buttons(main_layout)
        
        # منطقة عرض التقرير
        self.create_report_area(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام التدفقات النقدية")
    
    def create_title_bar_with_buttons(self, main_layout):
        """إنشاء شريط العنوان مع جميع الأزرار"""
        # العنوان الرئيسي مع الأزرار
        title_group = QGroupBox()
        title_layout = QVBoxLayout(title_group)
        
        # العنوان
        title_label = QLabel("💸 تقرير التدفقات النقدية (Cash Flow)")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_layout.addWidget(title_label)
        
        # شريط الأزرار والتحكم
        controls_layout = QHBoxLayout()
        

        
        # تاريخ البداية
        start_label = QLabel("من تاريخ:")
        start_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(start_label)

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.start_date.setFixedSize(150, 30)
        self.start_date.setCalendarPopup(True)
        controls_layout.addWidget(self.start_date)

        # تاريخ النهاية
        end_label = QLabel("إلى تاريخ:")
        end_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(end_label)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.end_date.setFixedSize(150, 30)
        self.end_date.setCalendarPopup(True)
        controls_layout.addWidget(self.end_date)
        
        # فاصل
        controls_layout.addWidget(QLabel(""))
        
        # زر تحديث التقرير
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setFixedSize(150, 30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_cash_flow_data)
        controls_layout.addWidget(refresh_btn)

        # زر طباعة التقرير
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setFixedSize(150, 30)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        controls_layout.addWidget(print_btn)
        
        title_layout.addLayout(controls_layout)
        main_layout.addWidget(title_group)



    def create_report_area(self, main_layout):
        """إنشاء منطقة عرض التقرير مع جدول واحد"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 14, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 6px 20px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب الحساب الرئيسي
        self.create_main_account_tab()

        # تبويب ملخصات التدفقات النقدية
        self.create_summary_tab()

        main_layout.addWidget(self.tab_widget)

    def create_main_account_tab(self):
        """إنشاء تبويب الحساب الرئيسي"""
        main_account_tab = QWidget()
        main_account_layout = QVBoxLayout(main_account_tab)

        # جدول الحساب الرئيسي
        self.main_account_table = QTableWidget()
        self.main_account_table.setColumnCount(6)
        self.main_account_table.setHorizontalHeaderLabels([
            "تاريخ العملية", "نوع العملية", "بيان العملية", "الدخول", "الخروج", "ملاحظات"
        ])

        # تنسيق جدول الحساب الرئيسي
        main_header = self.main_account_table.horizontalHeader()
        main_header.setFont(QFont("Calibri", 13, QFont.Bold))
        main_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 6px;
                border: 1px solid #e67e22;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
        """)

        self.main_account_table.setColumnWidth(0, 120)  # تاريخ العملية
        self.main_account_table.setColumnWidth(1, 150)  # نوع العملية
        self.main_account_table.setColumnWidth(2, 250)  # بيان العملية
        self.main_account_table.setColumnWidth(3, 120)  # الدخول
        self.main_account_table.setColumnWidth(4, 120)  # الخروج
        self.main_account_table.setColumnWidth(5, 300)  # ملاحظات

        self.main_account_table.setAlternatingRowColors(True)
        self.main_account_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.main_account_table.setFont(QFont("Calibri", 12))

        main_account_layout.addWidget(self.main_account_table)

        # إضافة التبويب
        self.tab_widget.addTab(main_account_tab, "التدفقات النقدية")

    def create_summary_tab(self):
        """إنشاء تبويب ملخصات التدفقات النقدية"""
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)

        # عنوان الملخص
        summary_title = QLabel("📊 ملخص التدفقات النقدية")
        summary_title.setFont(QFont("Calibri", 18, QFont.Bold))
        summary_title.setAlignment(Qt.AlignCenter)
        summary_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        summary_layout.addWidget(summary_title)

        # إنشاء البطاقات
        self.create_summary_cards(summary_layout)

        # إضافة التبويب
        self.tab_widget.addTab(summary_tab, "📊 ملخصات التدفقات النقدية")

    def create_summary_cards(self, layout):
        """إنشاء بطاقات ملخص التدفقات النقدية"""
        # تخطيط رئيسي للبطاقات
        cards_layout = QVBoxLayout()

        # الصف الأول: ثلاث بطاقات متقابلة
        top_row = QHBoxLayout()

        # بطاقة التدفقات الداخلة
        self.inflows_card = self.create_inflows_card()
        top_row.addWidget(self.inflows_card)

        # بطاقة التدفقات الخارجة
        self.outflows_card = self.create_outflows_card()
        top_row.addWidget(self.outflows_card)

        # بطاقة صافي التدفقات النقدية
        self.net_flow_card = self.create_net_flow_card()
        top_row.addWidget(self.net_flow_card)

        cards_layout.addLayout(top_row)

        layout.addLayout(cards_layout)

    def create_inflows_card(self):
        """إنشاء بطاقة التدفقات الداخلة"""
        card = QFrame()
        card.setFixedSize(350, 300)
        card.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 5px;
            }
        """)

        layout = QVBoxLayout(card)

        # عنوان البطاقة
        title = QLabel("💰 التدفقات الداخلة")
        title.setFont(QFont("Calibri", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #27ae60; margin-bottom: 5px;")
        layout.addWidget(title)

        # منطقة المحتوى
        self.inflows_content = QLabel("لا توجد بيانات")
        self.inflows_content.setFont(QFont("Calibri", 12, QFont.Bold))
        self.inflows_content.setAlignment(Qt.AlignTop)
        self.inflows_content.setWordWrap(True)
        self.inflows_content.setStyleSheet("color: #2c3e50; padding: 0px;")
        layout.addWidget(self.inflows_content)

        return card

    def create_outflows_card(self):
        """إنشاء بطاقة التدفقات الخارجة"""
        card = QFrame()
        card.setFixedSize(350, 300)
        card.setStyleSheet("""
            QFrame {
                background-color: #fde8e8;
                border: 2px solid #e74c3c;
                border-radius: 15px;
                padding: 5px;
            }
        """)

        layout = QVBoxLayout(card)

        # عنوان البطاقة
        title = QLabel("💸 التدفقات الخارجة")
        title.setFont(QFont("Calibri", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #e74c3c; margin-bottom: 5px;")
        layout.addWidget(title)

        # مجموع التدفقات الخارجة
        self.outflows_total = QLabel("إجمالي: 0.00 درهم")
        self.outflows_total.setFont(QFont("Calibri", 12, QFont.Bold))
        self.outflows_total.setAlignment(Qt.AlignCenter)
        self.outflows_total.setStyleSheet("color: #e74c3c; margin-bottom: 5px;")
        layout.addWidget(self.outflows_total)

        # منطقة المحتوى - 8 بطاقات في عمودين (4 في كل عمود)
        content_layout = QGridLayout()
        content_layout.setSpacing(2)
        self.outflows_content_widgets = []

        # إنشاء 8 بطاقات في عمودين (4 في كل عمود)
        for i in range(4):  # 4 صفوف
            for j in range(2):  # عمودين
                label = QLabel("")
                label.setFixedSize(140, 35)
                label.setFont(QFont("Calibri", 12, QFont.Bold))
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("""
                    QLabel {
                        color: #2c3e50;
                        background-color: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 8px;
                        padding: 0px;
                        margin: 1px;
                    }
                """)
                content_layout.addWidget(label, i, j)
                self.outflows_content_widgets.append(label)

        layout.addLayout(content_layout)

        return card

    def create_net_flow_card(self):
        """إنشاء بطاقة النتيجة"""
        card = QFrame()
        card.setFixedSize(350, 300)
        card.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 5px;
            }
        """)

        layout = QVBoxLayout(card)

        # بطاقة مجموع التدفقات الداخلية
        inflows_summary = QFrame()
        inflows_summary.setFixedSize(320, 35)
        inflows_summary.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin: 2px;
            }
        """)
        inflows_layout = QHBoxLayout(inflows_summary)
        inflows_layout.setContentsMargins(5, 0, 5, 0)

        inflows_title = QLabel("💰 مجموع التدفقات الداخلية:")
        inflows_title.setFont(QFont("Calibri", 12, QFont.Bold))
        inflows_title.setStyleSheet("color: #27ae60;")
        inflows_layout.addWidget(inflows_title)

        self.total_inflows_amount = QLabel("0.00 درهم")
        self.total_inflows_amount.setFont(QFont("Calibri", 12, QFont.Bold))
        self.total_inflows_amount.setAlignment(Qt.AlignRight)
        self.total_inflows_amount.setStyleSheet("color: #27ae60;")
        inflows_layout.addWidget(self.total_inflows_amount)

        layout.addWidget(inflows_summary)

        # بطاقة مجموع التدفقات الخارجية
        outflows_summary = QFrame()
        outflows_summary.setFixedSize(320, 35)
        outflows_summary.setStyleSheet("""
            QFrame {
                background-color: #fde8e8;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin: 2px;
            }
        """)
        outflows_layout = QHBoxLayout(outflows_summary)
        outflows_layout.setContentsMargins(5, 0, 5, 0)

        outflows_title = QLabel("💸 مجموع التدفقات الخارجية:")
        outflows_title.setFont(QFont("Calibri", 12, QFont.Bold))
        outflows_title.setStyleSheet("color: #e74c3c;")
        outflows_layout.addWidget(outflows_title)

        self.total_outflows_amount = QLabel("0.00 درهم")
        self.total_outflows_amount.setFont(QFont("Calibri", 12, QFont.Bold))
        self.total_outflows_amount.setAlignment(Qt.AlignRight)
        self.total_outflows_amount.setStyleSheet("color: #e74c3c;")
        outflows_layout.addWidget(self.total_outflows_amount)

        layout.addWidget(outflows_summary)

        # بطاقة النتيجة
        result_summary = QFrame()
        result_summary.setFixedSize(320, 35)
        result_summary.setStyleSheet("""
            QFrame {
                background-color: #e3f2fd;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin: 2px;
            }
        """)
        result_layout = QHBoxLayout(result_summary)
        result_layout.setContentsMargins(5, 0, 5, 0)

        result_title = QLabel("📊 النتيجة:")
        result_title.setFont(QFont("Calibri", 12, QFont.Bold))
        result_title.setStyleSheet("color: #3498db;")
        result_layout.addWidget(result_title)

        self.net_flow_amount = QLabel("0.00 درهم")
        self.net_flow_amount.setFont(QFont("Calibri", 12, QFont.Bold))
        self.net_flow_amount.setAlignment(Qt.AlignRight)
        self.net_flow_amount.setStyleSheet("color: #2c3e50;")
        result_layout.addWidget(self.net_flow_amount)

        layout.addWidget(result_summary)

        return card










    def load_cash_flow_data(self):
        """تحميل بيانات التدفقات النقدية"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            print(f"🔍 [DEBUG] تحميل البيانات من {start_date} إلى {end_date}")

            # تجميع البيانات في الجدول الرئيسي
            if not self.manager.populate_main_account_table(start_date, end_date):
                QMessageBox.critical(self, "خطأ", "فشل في تجميع البيانات في الجدول الرئيسي")
                return

            # جلب البيانات من الجدول الرئيسي
            main_account_data = self.manager.get_main_account_data(start_date, end_date)
            print(f"🔍 [DEBUG] عدد العمليات في الحساب الرئيسي: {len(main_account_data)}")

            # تحديث الجدول
            self.update_main_account_table(main_account_data)

            # إنشاء ملخص التدفقات
            self.generate_cash_flow_summary_from_main_account(main_account_data, start_date, end_date)

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل التدفقات النقدية من {start_date} إلى {end_date} - عدد العمليات: {len(main_account_data)}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التدفقات النقدية: {str(e)}")

    def update_main_account_table(self, main_account_data):
        """تحديث جدول الحساب الرئيسي"""
        self.main_account_table.setRowCount(len(main_account_data))

        for row, operation in enumerate(main_account_data):
            items = [
                operation['date'],
                operation['type'],
                operation['description'],
                f"{operation['inflow']:,.2f} درهم" if operation['inflow'] > 0 else "",
                f"{operation['outflow']:,.2f} درهم" if operation['outflow'] > 0 else "",
                operation['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 13, QFont.Bold))
                item.setForeground(QColor("#000000"))  # النص أسود غامق

                # تلوين المبالغ
                if col == 3 and operation['inflow'] > 0:  # الدخول
                    item.setForeground(QColor("#003366"))  # أزرق غامق
                    item.setFont(QFont("Calibri", 13, QFont.Bold))
                elif col == 4 and operation['outflow'] > 0:  # الخروج
                    item.setForeground(QColor("#990000"))  # أحمر غامق
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                self.main_account_table.setItem(row, col, item)

    def generate_cash_flow_summary_from_main_account(self, main_account_data, start_date, end_date):
        """إنشاء ملخص التدفقات النقدية من الجدول الرئيسي"""
        try:
            # حساب الإجماليات
            total_inflows = sum(operation['inflow'] for operation in main_account_data)
            total_outflows = sum(operation['outflow'] for operation in main_account_data)
            net_cash_flow = total_inflows - total_outflows

            # تحديث بطاقة التدفقات الداخلة
            inflow_types = {}
            for operation in main_account_data:
                if operation['inflow'] > 0:
                    op_type = operation['type']
                    if op_type not in inflow_types:
                        inflow_types[op_type] = 0
                    inflow_types[op_type] += operation['inflow']

            inflows_text = f"إجمالي: {total_inflows:,.2f} درهم\n\n"
            if inflow_types:
                for op_type, amount in inflow_types.items():
                    inflows_text += f"• {op_type}: {amount:,.2f} درهم\n"
            else:
                inflows_text += "لا توجد تدفقات داخلة"

            self.inflows_content.setText(inflows_text)

            # تحديث بطاقة التدفقات الخارجة
            outflow_types = {}
            for operation in main_account_data:
                if operation['outflow'] > 0:
                    op_type = operation['type']
                    if op_type not in outflow_types:
                        outflow_types[op_type] = 0
                    outflow_types[op_type] += operation['outflow']

            # تحديث مجموع التدفقات الخارجة
            self.outflows_total.setText(f"إجمالي: {total_outflows:,.2f} درهم")

            # تحديث عناصر التدفقات الخارجة في عمودين
            outflow_items = list(outflow_types.items())
            for i, widget in enumerate(self.outflows_content_widgets):
                if i < len(outflow_items):
                    op_type, amount = outflow_items[i]
                    widget.setText(f"{op_type}: {amount:,.0f}")
                else:
                    widget.setText("")

            # تحديث البطاقات الثلاث
            # بطاقة مجموع التدفقات الداخلية
            self.total_inflows_amount.setText(f"{total_inflows:,.2f} درهم")

            # بطاقة مجموع التدفقات الخارجية
            self.total_outflows_amount.setText(f"{total_outflows:,.2f} درهم")

            # بطاقة النتيجة
            self.net_flow_amount.setText(f"{net_cash_flow:,.2f} درهم")

            if net_cash_flow > 0:
                self.net_flow_amount.setStyleSheet("color: #27ae60;")
            elif net_cash_flow < 0:
                self.net_flow_amount.setStyleSheet("color: #e74c3c;")
            else:
                self.net_flow_amount.setStyleSheet("color: #3498db;")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء ملخص التدفقات: {str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QPrintDialog.Accepted:
                # طباعة محتوى الملخص
                self.summary_text.print_(printer)

                QMessageBox.information(self, "نجح", "تم طباعة التقرير بنجاح!")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة الطباعة غير متوفرة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")


def main():
    """الدالة الرئيسية"""
    import sys
    app = QApplication(sys.argv)

    # تطبيق الخط العربي
    app.setFont(QFont("Arial", 10))

    window = CashFlowWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
