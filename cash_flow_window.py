# نافذة تقرير التدفقات النقدية (Cash Flow Report)
# تحتوي على: تتبع الأموال الداخلة والخارجة، التحليل الزمني، التوقعات

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. المخططات البيانية لن تعمل.")

class CashFlowManager:
    """مدير بيانات التدفقات النقدية"""

    def __init__(self, db_path="data.db"):
        self.db_path = db_path

    def create_main_account_table(self):
        """إنشاء جدول الحساب الرئيسي"""
        try:
            print("🔍 [DEBUG] إنشاء جدول الحساب الرئيسي...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف الجدول القديم إذا كان موجوداً
            cursor.execute("DROP TABLE IF EXISTS الحساب_الرئيسي")

            # إنشاء الجدول الجديد
            cursor.execute("""
                CREATE TABLE الحساب_الرئيسي (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    تاريخ_العملية DATE NOT NULL,
                    نوع_العملية TEXT NOT NULL,
                    بيان_العملية TEXT NOT NULL,
                    الدخول REAL DEFAULT 0,
                    الخروج REAL DEFAULT 0,
                    ملاحظات TEXT,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم إنشاء جدول الحساب الرئيسي بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء جدول الحساب الرئيسي: {str(e)}")
            return False
    


    def populate_main_account_table(self, start_date, end_date):
        """تجميع البيانات من الجداول الثلاثة وإدراجها في الجدول الرئيسي"""
        try:
            print(f"🔍 [DEBUG] تجميع البيانات من {start_date} إلى {end_date}")

            # إنشاء جدول الحساب الرئيسي
            if not self.create_main_account_table():
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 1. جلب الواجبات الشهرية - تجميع حسب created_date والقسم
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='monthly_duties'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول monthly_duties...")
                cursor.execute("""
                    SELECT DATE(created_date) as تاريخ_التجميع,
                           القسم,
                           month,
                           SUM(amount_paid) as إجمالي_المبلغ
                    FROM monthly_duties
                    WHERE DATE(created_date) BETWEEN ? AND ?
                    AND amount_paid > 0
                    GROUP BY DATE(created_date), القسم, month
                    ORDER BY DATE(created_date) DESC
                """, (start_date, end_date))

                monthly_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد مجموعات الواجبات الشهرية: {len(monthly_results)}")

                for row in monthly_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # تاريخ_التجميع (created_date)
                        'الواجبات الشهرية',
                        row[2] or 'واجب شهري',  # month (بيان العملية)
                        float(row[3]) if row[3] else 0.0,  # إجمالي_المبلغ (الدخول)
                        0,  # الخروج
                        row[1] or ''  # القسم (ملاحظات)
                    ))

            # 2. جلب رسوم التسجيل
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='registration_fees'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول registration_fees...")
                cursor.execute("""
                    SELECT payment_date, amount_paid, payment_type, notes
                    FROM registration_fees
                    WHERE payment_date BETWEEN ? AND ?
                    AND amount_paid > 0
                """, (start_date, end_date))

                registration_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد رسوم التسجيل: {len(registration_results)}")

                for row in registration_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # payment_date
                        'رسوم تسجيل',
                        f"رسوم تسجيل - {row[2]}",  # payment_type
                        float(row[1]) if row[1] else 0.0,  # amount_paid
                        0,
                        row[3] or ''  # notes
                    ))

            # 3. جلب المصاريف
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='المصاريف'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول المصاريف...")
                cursor.execute("""
                    SELECT التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة, ملاحظات
                    FROM المصاريف
                    WHERE التاريخ BETWEEN ? AND ?
                """, (start_date, end_date))

                expenses_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد المصاريف: {len(expenses_results)}")

                for row in expenses_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # التاريخ
                        row[2] or 'مصروف عام',  # نوع_المصروف
                        f"{row[2]} - {row[3]}" if row[3] else row[2],  # بيان مع الجهة المستفيدة
                        0,
                        float(row[1]) if row[1] else 0.0,  # المبلغ
                        row[4] or ''  # ملاحظات
                    ))

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم تجميع البيانات في الجدول الرئيسي بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تجميع البيانات: {str(e)}")
            return False

    def get_main_account_data(self, start_date, end_date):
        """جلب البيانات من جدول الحساب الرئيسي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT تاريخ_العملية, نوع_العملية, بيان_العملية,
                       الدخول, الخروج, ملاحظات
                FROM الحساب_الرئيسي
                WHERE تاريخ_العملية BETWEEN ? AND ?
                ORDER BY تاريخ_العملية DESC
            """, (start_date, end_date))

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    'date': row[0],
                    'type': row[1],
                    'description': row[2],
                    'inflow': float(row[3]) if row[3] else 0.0,
                    'outflow': float(row[4]) if row[4] else 0.0,
                    'notes': row[5] or ''
                }
                for row in results
            ]

        except Exception as e:
            print(f"❌ [ERROR] خطأ في جلب بيانات الحساب الرئيسي: {str(e)}")
            return []



class CashFlowWindow(QMainWindow):
    """نافذة تقرير التدفقات النقدية"""
    
    def __init__(self):
        super().__init__()
        self.manager = CashFlowManager()
        self.init_ui()
        self.load_cash_flow_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💸 تقرير التدفقات النقدية (Cash Flow)")
        self.setGeometry(100, 100, 1400, 900)
        
        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #87ceeb;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # شريط العنوان مع الأزرار
        self.create_title_bar_with_buttons(main_layout)
        
        # منطقة عرض التقرير
        self.create_report_area(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام التدفقات النقدية")
    
    def create_title_bar_with_buttons(self, main_layout):
        """إنشاء شريط العنوان مع جميع الأزرار"""
        # العنوان الرئيسي مع الأزرار
        title_group = QGroupBox()
        title_layout = QVBoxLayout(title_group)
        
        # العنوان
        title_label = QLabel("💸 تقرير التدفقات النقدية (Cash Flow)")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_layout.addWidget(title_label)
        
        # شريط الأزرار والتحكم
        controls_layout = QHBoxLayout()
        

        
        # تاريخ البداية
        start_label = QLabel("من تاريخ:")
        start_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(start_label)

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.start_date.setFixedSize(150, 30)
        self.start_date.setCalendarPopup(True)
        controls_layout.addWidget(self.start_date)

        # تاريخ النهاية
        end_label = QLabel("إلى تاريخ:")
        end_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(end_label)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.end_date.setFixedSize(150, 30)
        self.end_date.setCalendarPopup(True)
        controls_layout.addWidget(self.end_date)
        
        # فاصل
        controls_layout.addWidget(QLabel(""))
        
        # زر تحديث التقرير
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setFixedSize(150, 30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_cash_flow_data)
        controls_layout.addWidget(refresh_btn)

        # زر طباعة التقرير
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setFixedSize(150, 30)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        controls_layout.addWidget(print_btn)
        
        title_layout.addLayout(controls_layout)
        main_layout.addWidget(title_group)



    def create_report_area(self, main_layout):
        """إنشاء منطقة عرض التقرير مع جدول واحد"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 14, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 6px 20px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب الحساب الرئيسي
        self.create_main_account_tab()

        # تبويب ملخصات التدفقات النقدية
        self.create_summary_tab()

        main_layout.addWidget(self.tab_widget)

    def create_main_account_tab(self):
        """إنشاء تبويب الحساب الرئيسي"""
        main_account_tab = QWidget()
        main_account_layout = QVBoxLayout(main_account_tab)

        # جدول الحساب الرئيسي
        self.main_account_table = QTableWidget()
        self.main_account_table.setColumnCount(6)
        self.main_account_table.setHorizontalHeaderLabels([
            "تاريخ العملية", "نوع العملية", "بيان العملية", "الدخول", "الخروج", "ملاحظات"
        ])

        # تنسيق جدول الحساب الرئيسي
        main_header = self.main_account_table.horizontalHeader()
        main_header.setFont(QFont("Calibri", 13, QFont.Bold))
        main_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 6px;
                border: 1px solid #e67e22;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
        """)

        self.main_account_table.setColumnWidth(0, 120)  # تاريخ العملية
        self.main_account_table.setColumnWidth(1, 150)  # نوع العملية
        self.main_account_table.setColumnWidth(2, 250)  # بيان العملية
        self.main_account_table.setColumnWidth(3, 120)  # الدخول
        self.main_account_table.setColumnWidth(4, 120)  # الخروج
        self.main_account_table.setColumnWidth(5, 300)  # ملاحظات

        self.main_account_table.setAlternatingRowColors(True)
        self.main_account_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.main_account_table.setFont(QFont("Calibri", 12))

        main_account_layout.addWidget(self.main_account_table)

        # إضافة التبويب
        self.tab_widget.addTab(main_account_tab, "� الحساب الرئيسي")

    def create_summary_tab(self):
        """إنشاء تبويب ملخصات التدفقات النقدية"""
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)



        # منطقة النص للملخص
        self.summary_text = QTextEdit()
        self.summary_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.summary_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        summary_layout.addWidget(self.summary_text)

        # إزالة الرسم البياني من الملخصات كما طلب المستخدم

        # إضافة التبويب
        self.tab_widget.addTab(summary_tab, "📊 ملخصات التدفقات النقدية")













    def load_cash_flow_data(self):
        """تحميل بيانات التدفقات النقدية"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            print(f"🔍 [DEBUG] تحميل البيانات من {start_date} إلى {end_date}")

            # تجميع البيانات في الجدول الرئيسي
            if not self.manager.populate_main_account_table(start_date, end_date):
                QMessageBox.critical(self, "خطأ", "فشل في تجميع البيانات في الجدول الرئيسي")
                return

            # جلب البيانات من الجدول الرئيسي
            main_account_data = self.manager.get_main_account_data(start_date, end_date)
            print(f"🔍 [DEBUG] عدد العمليات في الحساب الرئيسي: {len(main_account_data)}")

            # تحديث الجدول
            self.update_main_account_table(main_account_data)

            # إنشاء ملخص التدفقات
            self.generate_cash_flow_summary_from_main_account(main_account_data, start_date, end_date)

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل التدفقات النقدية من {start_date} إلى {end_date} - عدد العمليات: {len(main_account_data)}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التدفقات النقدية: {str(e)}")

    def update_main_account_table(self, main_account_data):
        """تحديث جدول الحساب الرئيسي"""
        self.main_account_table.setRowCount(len(main_account_data))

        for row, operation in enumerate(main_account_data):
            items = [
                operation['date'],
                operation['type'],
                operation['description'],
                f"{operation['inflow']:,.2f} درهم" if operation['inflow'] > 0 else "",
                f"{operation['outflow']:,.2f} درهم" if operation['outflow'] > 0 else "",
                operation['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 12))

                # تلوين المبالغ
                if col == 3 and operation['inflow'] > 0:  # الدخول
                    item.setForeground(QColor("#27ae60"))
                    item.setFont(QFont("Calibri", 12, QFont.Bold))
                elif col == 4 and operation['outflow'] > 0:  # الخروج
                    item.setForeground(QColor("#e74c3c"))
                    item.setFont(QFont("Calibri", 12, QFont.Bold))

                self.main_account_table.setItem(row, col, item)

    def generate_cash_flow_summary_from_main_account(self, main_account_data, start_date, end_date):
        """إنشاء ملخص التدفقات النقدية من الجدول الرئيسي"""
        try:
            from datetime import datetime

            # حساب الإجماليات
            total_inflows = sum(operation['inflow'] for operation in main_account_data)
            total_outflows = sum(operation['outflow'] for operation in main_account_data)
            net_cash_flow = total_inflows - total_outflows

            # بناء التقرير
            summary = f"💸 تقرير التدفقات النقدية (Cash Flow)\n"
            summary += f"الفترة: من {start_date} إلى {end_date}\n"
            summary += f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            summary += "=" * 60 + "\n\n"

            # ملخص التدفقات الداخلة
            summary += "💰 التدفقات الداخلة:\n"
            summary += "-" * 30 + "\n"

            inflow_types = {}
            for operation in main_account_data:
                if operation['inflow'] > 0:
                    op_type = operation['type']
                    if op_type not in inflow_types:
                        inflow_types[op_type] = 0
                    inflow_types[op_type] += operation['inflow']

            if inflow_types:
                for op_type, amount in inflow_types.items():
                    summary += f"• {op_type}: {amount:,.2f} درهم\n"
            else:
                summary += "لا توجد تدفقات داخلة في هذه الفترة\n"

            summary += f"\nإجمالي التدفقات الداخلة: {total_inflows:,.2f} درهم\n\n"

            # ملخص التدفقات الخارجة
            summary += "💸 التدفقات الخارجة:\n"
            summary += "-" * 30 + "\n"

            outflow_types = {}
            for operation in main_account_data:
                if operation['outflow'] > 0:
                    op_type = operation['type']
                    if op_type not in outflow_types:
                        outflow_types[op_type] = 0
                    outflow_types[op_type] += operation['outflow']

            if outflow_types:
                for op_type, amount in outflow_types.items():
                    summary += f"• {op_type}: {amount:,.2f} درهم\n"
            else:
                summary += "لا توجد تدفقات خارجة في هذه الفترة\n"

            summary += f"\nإجمالي التدفقات الخارجة: {total_outflows:,.2f} درهم\n\n"

            # صافي التدفق النقدي
            summary += "📊 صافي التدفق النقدي:\n"
            summary += "-" * 30 + "\n"
            summary += f"صافي التدفق النقدي: {net_cash_flow:,.2f} درهم\n"

            if net_cash_flow > 0:
                summary += "✅ التدفق النقدي إيجابي (ربح)\n"
            elif net_cash_flow < 0:
                summary += "⚠️ التدفق النقدي سلبي (خسارة)\n"
            else:
                summary += "⚖️ التدفق النقدي متوازن\n"

            # عرض الملخص
            self.summary_text.setPlainText(summary)

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء ملخص التدفقات: {str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QPrintDialog.Accepted:
                # طباعة محتوى الملخص
                self.summary_text.print_(printer)

                QMessageBox.information(self, "نجح", "تم طباعة التقرير بنجاح!")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة الطباعة غير متوفرة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")


def main():
    """الدالة الرئيسية"""
    import sys
    app = QApplication(sys.argv)

    # تطبيق الخط العربي
    app.setFont(QFont("Arial", 10))

    window = CashFlowWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
