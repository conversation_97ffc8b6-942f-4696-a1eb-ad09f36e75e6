# نافذة تقرير التدفقات النقدية (Cash Flow Report)
# تحتوي على: تتبع الأموال الداخلة والخارجة، التحليل الزمني، التوقعات

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. المخططات البيانية لن تعمل.")

class CashFlowManager:
    """مدير بيانات التدفقات النقدية"""

    def __init__(self, db_path="data.db"):
        self.db_path = db_path
    

    
    def get_cash_inflows(self, start_date, end_date):
        """جلب التدفقات الداخلة من جداول الواجبات الشهرية ورسوم التسجيل"""
        try:
            print(f"🔍 [DEBUG] البحث عن التدفقات الداخلة من {start_date} إلى {end_date}")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            inflows = []

            # التحقق من وجود جدول الواجبات الشهرية
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='monthly_duties'
            """)
            monthly_table_exists = cursor.fetchone()
            print(f"🔍 [DEBUG] جدول monthly_duties موجود: {bool(monthly_table_exists)}")

            # جلب الواجبات الشهرية المدفوعة
            if monthly_table_exists:
                try:
                    cursor.execute("""
                        SELECT payment_date, amount_paid, 'الواجبات الشهرية' as source,
                               'نقدي' as method, notes
                        FROM monthly_duties
                        WHERE payment_date BETWEEN ? AND ?
                        AND payment_status = 'مدفوع'
                        AND amount_paid > 0
                        ORDER BY payment_date DESC
                    """, (start_date, end_date))

                    monthly_results = cursor.fetchall()
                    print(f"🔍 [DEBUG] عدد الواجبات الشهرية المدفوعة: {len(monthly_results)}")

                    for row in monthly_results:
                        inflows.append({
                            'date': row[0],
                            'amount': float(row[1]) if row[1] else 0.0,
                            'source': row[2],
                            'method': row[3],
                            'notes': row[4] or ''
                        })
                except sqlite3.Error as e:
                    print(f"❌ [ERROR] خطأ في جلب الواجبات الشهرية: {str(e)}")

            # التحقق من وجود جدول رسوم التسجيل
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='registration_fees'
            """)
            registration_table_exists = cursor.fetchone()
            print(f"🔍 [DEBUG] جدول registration_fees موجود: {bool(registration_table_exists)}")

            # جلب رسوم التسجيل
            if registration_table_exists:
                try:
                    cursor.execute("""
                        SELECT payment_date, amount_paid, payment_type as source,
                               payment_method as method, notes
                        FROM registration_fees
                        WHERE payment_date BETWEEN ? AND ?
                        AND amount_paid > 0
                        ORDER BY payment_date DESC
                    """, (start_date, end_date))

                    registration_results = cursor.fetchall()
                    print(f"🔍 [DEBUG] عدد رسوم التسجيل المدفوعة: {len(registration_results)}")

                    for row in registration_results:
                        inflows.append({
                            'date': row[0],
                            'amount': float(row[1]) if row[1] else 0.0,
                            'source': f"رسوم التسجيل - {row[2]}",
                            'method': row[3] or 'نقدي',
                            'notes': row[4] or ''
                        })
                except sqlite3.Error as e:
                    print(f"❌ [ERROR] خطأ في جلب رسوم التسجيل: {str(e)}")

            conn.close()

            # ترتيب النتائج حسب التاريخ
            inflows.sort(key=lambda x: x['date'], reverse=True)
            print(f"🔍 [DEBUG] إجمالي التدفقات الداخلة: {len(inflows)}")
            return inflows

        except Exception as e:
            print(f"❌ [ERROR] خطأ في جلب التدفقات الداخلة: {str(e)}")
            return []
    
    def get_cash_outflows(self, start_date, end_date):
        """جلب التدفقات الخارجة من جدول المصاريف"""
        try:
            print(f"🔍 [DEBUG] البحث عن التدفقات الخارجة من {start_date} إلى {end_date}")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول المصاريف
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='المصاريف'
            """)
            expenses_table_exists = cursor.fetchone()
            print(f"🔍 [DEBUG] جدول المصاريف موجود: {bool(expenses_table_exists)}")

            if not expenses_table_exists:
                conn.close()
                return []

            try:
                cursor.execute("""
                    SELECT التاريخ, المبلغ, نوع_المصروف, طريقة_الأداء,
                           الجهة_المستفيدة, ملاحظات
                    FROM المصاريف
                    WHERE التاريخ BETWEEN ? AND ?
                    ORDER BY التاريخ DESC
                """, (start_date, end_date))

                results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد المصاريف الموجودة: {len(results)}")
                conn.close()

                return [
                    {
                        'date': row[0],
                        'amount': float(row[1]) if row[1] else 0.0,
                        'type': row[2] or 'مصروف عام',
                        'method': row[3] or 'نقدي',
                        'beneficiary': row[4] or '',
                        'notes': row[5] or ''
                    }
                    for row in results
                ]

            except sqlite3.Error as e:
                print(f"❌ [ERROR] خطأ في الاستعلام عن المصاريف: {str(e)}")
                conn.close()
                return []

        except Exception as e:
            print(f"❌ [ERROR] خطأ في جلب التدفقات الخارجة: {str(e)}")
            return []

class CashFlowWindow(QMainWindow):
    """نافذة تقرير التدفقات النقدية"""
    
    def __init__(self):
        super().__init__()
        self.manager = CashFlowManager()
        self.init_ui()
        self.load_cash_flow_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💸 تقرير التدفقات النقدية (Cash Flow)")
        self.setGeometry(100, 100, 1400, 900)
        
        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #87ceeb;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # شريط العنوان مع الأزرار
        self.create_title_bar_with_buttons(main_layout)
        
        # منطقة عرض التقرير
        self.create_report_area(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام التدفقات النقدية")
    
    def create_title_bar_with_buttons(self, main_layout):
        """إنشاء شريط العنوان مع جميع الأزرار"""
        # العنوان الرئيسي مع الأزرار
        title_group = QGroupBox()
        title_layout = QVBoxLayout(title_group)
        
        # العنوان
        title_label = QLabel("💸 تقرير التدفقات النقدية (Cash Flow)")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_layout.addWidget(title_label)
        
        # شريط الأزرار والتحكم
        controls_layout = QHBoxLayout()
        
        # أزرار التواريخ السريعة
        today_btn = QPushButton("اليوم")
        today_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        today_btn.setFixedSize(150, 30)
        today_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        today_btn.clicked.connect(self.set_today)
        controls_layout.addWidget(today_btn)

        week_btn = QPushButton("هذا الأسبوع")
        week_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        week_btn.setFixedSize(150, 30)
        week_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
        """)
        week_btn.clicked.connect(self.set_this_week)
        controls_layout.addWidget(week_btn)

        month_btn = QPushButton("هذا الشهر")
        month_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        month_btn.setFixedSize(150, 30)
        month_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #e55a00;
            }
        """)
        month_btn.clicked.connect(self.set_this_month)
        controls_layout.addWidget(month_btn)
        
        # فاصل
        controls_layout.addWidget(QLabel(""))
        
        # تاريخ البداية
        start_label = QLabel("من تاريخ:")
        start_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(start_label)

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.start_date.setFixedSize(150, 30)
        self.start_date.setCalendarPopup(True)
        controls_layout.addWidget(self.start_date)

        # تاريخ النهاية
        end_label = QLabel("إلى تاريخ:")
        end_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(end_label)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.end_date.setFixedSize(150, 30)
        self.end_date.setCalendarPopup(True)
        controls_layout.addWidget(self.end_date)
        
        # فاصل
        controls_layout.addWidget(QLabel(""))
        
        # زر تحديث التقرير
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setFixedSize(150, 30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_cash_flow_data)
        controls_layout.addWidget(refresh_btn)

        # زر طباعة التقرير
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setFixedSize(150, 30)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        controls_layout.addWidget(print_btn)
        
        title_layout.addLayout(controls_layout)
        main_layout.addWidget(title_group)

        # ربط تغيير التاريخ بتحديث البيانات
        self.start_date.dateChanged.connect(self.load_cash_flow_data)
        self.end_date.dateChanged.connect(self.load_cash_flow_data)

    def create_report_area(self, main_layout):
        """إنشاء منطقة عرض التقرير مع التبويبات"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 14, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 6px 20px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب التدفقات الداخلة
        self.create_inflows_tab()

        # تبويب التدفقات الخارجة
        self.create_outflows_tab()

        # تبويب ملخصات التدفقات النقدية
        self.create_summary_tab()

        main_layout.addWidget(self.tab_widget)

    def create_inflows_tab(self):
        """إنشاء تبويب التدفقات الداخلة"""
        inflows_tab = QWidget()
        inflows_layout = QVBoxLayout(inflows_tab)



        # جدول التدفقات الداخلة
        self.inflows_table = QTableWidget()
        self.inflows_table.setColumnCount(5)
        self.inflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "المصدر", "طريقة الدفع", "ملاحظات"
        ])

        # تنسيق جدول التدفقات الداخلة
        inflows_header = self.inflows_table.horizontalHeader()
        inflows_header.setFont(QFont("Calibri", 13, QFont.Bold))
        inflows_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 6px;
                border: 1px solid #e67e22;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
        """)

        self.inflows_table.setColumnWidth(0, 120)  # التاريخ
        self.inflows_table.setColumnWidth(1, 150)  # المبلغ
        self.inflows_table.setColumnWidth(2, 200)  # المصدر
        self.inflows_table.setColumnWidth(3, 150)  # طريقة الدفع
        self.inflows_table.setColumnWidth(4, 300)  # ملاحظات

        self.inflows_table.setAlternatingRowColors(True)
        self.inflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.inflows_table.setFont(QFont("Calibri", 12))

        inflows_layout.addWidget(self.inflows_table)

        # إضافة التبويب
        self.tab_widget.addTab(inflows_tab, "💰 التدفقات الداخلة")

    def create_outflows_tab(self):
        """إنشاء تبويب التدفقات الخارجة"""
        outflows_tab = QWidget()
        outflows_layout = QVBoxLayout(outflows_tab)



        # جدول التدفقات الخارجة
        self.outflows_table = QTableWidget()
        self.outflows_table.setColumnCount(6)
        self.outflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "نوع المصروف", "طريقة الدفع", "المستفيد", "ملاحظات"
        ])

        # تنسيق جدول التدفقات الخارجة
        outflows_header = self.outflows_table.horizontalHeader()
        outflows_header.setFont(QFont("Calibri", 13, QFont.Bold))
        outflows_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 6px;
                border: 1px solid #e67e22;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
        """)

        self.outflows_table.setColumnWidth(0, 120)  # التاريخ
        self.outflows_table.setColumnWidth(1, 150)  # المبلغ
        self.outflows_table.setColumnWidth(2, 180)  # نوع المصروف
        self.outflows_table.setColumnWidth(3, 150)  # طريقة الدفع
        self.outflows_table.setColumnWidth(4, 200)  # المستفيد
        self.outflows_table.setColumnWidth(5, 300)  # ملاحظات

        self.outflows_table.setAlternatingRowColors(True)
        self.outflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.outflows_table.setFont(QFont("Calibri", 12))

        outflows_layout.addWidget(self.outflows_table)

        # إضافة التبويب
        self.tab_widget.addTab(outflows_tab, "💸 التدفقات الخارجة")

    def create_summary_tab(self):
        """إنشاء تبويب ملخصات التدفقات النقدية"""
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)



        # منطقة النص للملخص
        self.summary_text = QTextEdit()
        self.summary_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.summary_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        summary_layout.addWidget(self.summary_text)

        # إزالة الرسم البياني من الملخصات كما طلب المستخدم

        # إضافة التبويب
        self.tab_widget.addTab(summary_tab, "📊 ملخصات التدفقات النقدية")

    def create_inflows_chart(self, layout):
        """إنشاء مخطط بياني للتدفقات الداخلة"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

            # إنشاء الرسم البياني
            fig = Figure(figsize=(10, 4), dpi=100)
            canvas = FigureCanvas(fig)

            # إضافة الرسم البياني إلى التخطيط
            layout.addWidget(canvas)

            # حفظ المرجع للتحديث لاحقاً
            self.inflows_chart = fig
            self.inflows_canvas = canvas

        except Exception as e:
            print(f"خطأ في إنشاء مخطط التدفقات الداخلة: {str(e)}")

    def create_outflows_chart(self, layout):
        """إنشاء مخطط بياني للتدفقات الخارجة"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

            # إنشاء الرسم البياني
            fig = Figure(figsize=(10, 4), dpi=100)
            canvas = FigureCanvas(fig)

            # إضافة الرسم البياني إلى التخطيط
            layout.addWidget(canvas)

            # حفظ المرجع للتحديث لاحقاً
            self.outflows_chart = fig
            self.outflows_canvas = canvas

        except Exception as e:
            print(f"خطأ في إنشاء مخطط التدفقات الخارجة: {str(e)}")





    def update_inflows_chart(self, inflows):
        """تحديث مخطط التدفقات الداخلة"""
        try:
            self.inflows_chart.clear()
            ax = self.inflows_chart.add_subplot(111)

            if inflows:
                # تجميع البيانات حسب المصدر
                sources = {}
                for inflow in inflows:
                    source = inflow['source']
                    if source not in sources:
                        sources[source] = 0
                    sources[source] += inflow['amount']

                # إنشاء الرسم البياني الدائري
                labels = list(sources.keys())
                sizes = list(sources.values())
                colors = ['#27ae60', '#2ecc71', '#58d68d', '#82e0aa', '#abebc6']

                ax.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%', startangle=90)
                ax.set_title('توزيع التدفقات الداخلة حسب المصدر', fontsize=14, fontweight='bold')
            else:
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', ha='center', va='center', transform=ax.transAxes)
                ax.set_title('التدفقات الداخلة', fontsize=14, fontweight='bold')

            self.inflows_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث مخطط التدفقات الداخلة: {str(e)}")

    def update_outflows_chart(self, outflows):
        """تحديث مخطط التدفقات الخارجة"""
        try:
            self.outflows_chart.clear()
            ax = self.outflows_chart.add_subplot(111)

            if outflows:
                # تجميع البيانات حسب نوع المصروف
                types = {}
                for outflow in outflows:
                    expense_type = outflow['type']
                    if expense_type not in types:
                        types[expense_type] = 0
                    types[expense_type] += outflow['amount']

                # إنشاء الرسم البياني الدائري
                labels = list(types.keys())
                sizes = list(types.values())
                colors = ['#e74c3c', '#ec7063', '#f1948a', '#f5b7b1', '#fadbd8']

                ax.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%', startangle=90)
                ax.set_title('توزيع التدفقات الخارجة حسب النوع', fontsize=14, fontweight='bold')
            else:
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', ha='center', va='center', transform=ax.transAxes)
                ax.set_title('التدفقات الخارجة', fontsize=14, fontweight='bold')

            self.outflows_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث مخطط التدفقات الخارجة: {str(e)}")



    def set_today(self):
        """تعيين تاريخ اليوم"""
        today = QDate.currentDate()
        self.start_date.setDate(today)
        self.end_date.setDate(today)

    def set_this_week(self):
        """تعيين هذا الأسبوع"""
        today = QDate.currentDate()
        start_of_week = today.addDays(-today.dayOfWeek() + 1)
        self.start_date.setDate(start_of_week)
        self.end_date.setDate(today)

    def set_this_month(self):
        """تعيين هذا الشهر"""
        today = QDate.currentDate()
        start_of_month = QDate(today.year(), today.month(), 1)
        self.start_date.setDate(start_of_month)
        self.end_date.setDate(today)

    def load_cash_flow_data(self):
        """تحميل بيانات التدفقات النقدية"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            print(f"🔍 [DEBUG] تحميل البيانات من {start_date} إلى {end_date}")

            # جلب البيانات
            inflows = self.manager.get_cash_inflows(start_date, end_date)
            outflows = self.manager.get_cash_outflows(start_date, end_date)

            print(f"🔍 [DEBUG] عدد التدفقات الداخلة: {len(inflows)}")
            print(f"🔍 [DEBUG] عدد التدفقات الخارجة: {len(outflows)}")

            # تحديث الجداول
            self.update_inflows_table(inflows)
            self.update_outflows_table(outflows)

            # إنشاء ملخص التدفقات
            self.generate_cash_flow_summary(inflows, outflows, start_date, end_date)

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل التدفقات النقدية من {start_date} إلى {end_date} - داخلة: {len(inflows)}, خارجة: {len(outflows)}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التدفقات النقدية: {str(e)}")

    def update_inflows_table(self, inflows):
        """تحديث جدول التدفقات الداخلة"""
        self.inflows_table.setRowCount(len(inflows))

        for row, inflow in enumerate(inflows):
            items = [
                inflow['date'],
                f"{inflow['amount']:,.2f} درهم",
                inflow['source'],
                inflow['method'],
                inflow['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 12))

                # تلوين المبلغ بالأخضر
                if col == 1:
                    item.setForeground(QColor("#27ae60"))
                    item.setFont(QFont("Calibri", 12, QFont.Bold))

                self.inflows_table.setItem(row, col, item)

    def update_outflows_table(self, outflows):
        """تحديث جدول التدفقات الخارجة"""
        self.outflows_table.setRowCount(len(outflows))

        for row, outflow in enumerate(outflows):
            items = [
                outflow['date'],
                f"{outflow['amount']:,.2f} درهم",
                outflow['type'],
                outflow['method'],
                outflow['beneficiary'],
                outflow['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 12))

                # تلوين المبلغ بالأحمر
                if col == 1:
                    item.setForeground(QColor("#e74c3c"))
                    item.setFont(QFont("Calibri", 12, QFont.Bold))

                self.outflows_table.setItem(row, col, item)

    def generate_cash_flow_summary(self, inflows, outflows, start_date, end_date):
        """إنشاء ملخص التدفقات النقدية"""
        try:
            from datetime import datetime

            # حساب الإجماليات
            total_inflows = sum(inflow['amount'] for inflow in inflows)
            total_outflows = sum(outflow['amount'] for outflow in outflows)
            net_cash_flow = total_inflows - total_outflows

            # بناء التقرير
            summary = f"💸 تقرير التدفقات النقدية (Cash Flow)\n"
            summary += f"الفترة: من {start_date} إلى {end_date}\n"
            summary += f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            summary += "=" * 60 + "\n\n"

            # ملخص التدفقات الداخلة
            summary += "💰 التدفقات الداخلة:\n"
            summary += "-" * 30 + "\n"

            if inflows:
                inflow_sources = {}
                for inflow in inflows:
                    source = inflow['source']
                    if source not in inflow_sources:
                        inflow_sources[source] = 0
                    inflow_sources[source] += inflow['amount']

                for source, amount in inflow_sources.items():
                    summary += f"• {source}: {amount:,.2f} درهم\n"
            else:
                summary += "لا توجد تدفقات داخلة في هذه الفترة\n"

            summary += f"\nإجمالي التدفقات الداخلة: {total_inflows:,.2f} درهم\n\n"

            # ملخص التدفقات الخارجة
            summary += "💸 التدفقات الخارجة:\n"
            summary += "-" * 30 + "\n"

            if outflows:
                outflow_types = {}
                for outflow in outflows:
                    expense_type = outflow['type']
                    if expense_type not in outflow_types:
                        outflow_types[expense_type] = 0
                    outflow_types[expense_type] += outflow['amount']

                for expense_type, amount in outflow_types.items():
                    summary += f"• {expense_type}: {amount:,.2f} درهم\n"
            else:
                summary += "لا توجد تدفقات خارجة في هذه الفترة\n"

            summary += f"\nإجمالي التدفقات الخارجة: {total_outflows:,.2f} درهم\n\n"

            # صافي التدفق النقدي
            summary += "📊 صافي التدفق النقدي:\n"
            summary += "-" * 30 + "\n"
            summary += f"صافي التدفق النقدي: {net_cash_flow:,.2f} درهم\n"

            if net_cash_flow > 0:
                summary += "✅ التدفق النقدي إيجابي (ربح)\n"
            elif net_cash_flow < 0:
                summary += "⚠️ التدفق النقدي سلبي (خسارة)\n"
            else:
                summary += "⚖️ التدفق النقدي متوازن\n"

            # عرض الملخص
            self.summary_text.setPlainText(summary)

        except Exception as e:
            print(f"خطأ في إنشاء ملخص التدفقات: {str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QPrintDialog.Accepted:
                # طباعة محتوى الملخص
                self.summary_text.print_(printer)

                QMessageBox.information(self, "نجح", "تم طباعة التقرير بنجاح!")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "مكتبة الطباعة غير متوفرة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")


def main():
    """الدالة الرئيسية"""
    import sys
    app = QApplication(sys.argv)

    # تطبيق الخط العربي
    app.setFont(QFont("Arial", 10))

    window = CashFlowWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
