# نافذة تقرير التدفقات النقدية (Cash Flow Report)
# تحتوي على: تتبع الأموال الداخلة والخارجة، التحليل الزمني، التوقعات

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. المخططات البيانية لن تعمل.")

class CashFlowManager:
    """مدير بيانات التدفقات النقدية"""

    def __init__(self, db_path="data.db"):
        self.db_path = db_path

    def create_main_account_table(self):
        """إنشاء جدول الحساب الرئيسي"""
        try:
            print("🔍 [DEBUG] إنشاء جدول الحساب الرئيسي...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف الجدول القديم إذا كان موجوداً
            cursor.execute("DROP TABLE IF EXISTS الحساب_الرئيسي")

            # إنشاء الجدول الجديد
            cursor.execute("""
                CREATE TABLE الحساب_الرئيسي (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    تاريخ_العملية DATE NOT NULL,
                    نوع_العملية TEXT NOT NULL,
                    بيان_العملية TEXT NOT NULL,
                    الدخول REAL DEFAULT 0,
                    الخروج REAL DEFAULT 0,
                    ملاحظات TEXT,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم إنشاء جدول الحساب الرئيسي بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء جدول الحساب الرئيسي: {str(e)}")
            return False

    def create_withdrawals_table(self):
        """إنشاء جدول المسحوبات"""
        try:
            print("🔍 [DEBUG] إنشاء جدول المسحوبات...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء الجدول إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS المسحوبات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    تاريخ_العملية DATE NOT NULL,
                    المبلغ_المسحوب REAL NOT NULL,
                    البيان TEXT NOT NULL,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم إنشاء جدول المسحوبات بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء جدول المسحوبات: {str(e)}")
            return False
    


    def populate_main_account_table(self, start_date, end_date):
        """تجميع البيانات من الجداول الثلاثة وإدراجها في الجدول الرئيسي"""
        try:
            print(f"🔍 [DEBUG] تجميع البيانات من {start_date} إلى {end_date}")

            # إنشاء جدول الحساب الرئيسي
            if not self.create_main_account_table():
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 1. جلب الواجبات الشهرية - تجميع حسب created_date والقسم
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='monthly_duties'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول monthly_duties...")
                cursor.execute("""
                    SELECT DATE(created_date) as تاريخ_التجميع,
                           القسم,
                           month,
                           SUM(amount_paid) as إجمالي_المبلغ
                    FROM monthly_duties
                    WHERE DATE(created_date) BETWEEN ? AND ?
                    AND amount_paid > 0
                    GROUP BY DATE(created_date), القسم, month
                    ORDER BY DATE(created_date) DESC
                """, (start_date, end_date))

                monthly_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد مجموعات الواجبات الشهرية: {len(monthly_results)}")

                for row in monthly_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # تاريخ_التجميع (created_date)
                        'الواجبات الشهرية',
                        row[2] or 'واجب شهري',  # month (بيان العملية)
                        float(row[3]) if row[3] else 0.0,  # إجمالي_المبلغ (الدخول)
                        0,  # الخروج
                        row[1] or ''  # القسم (ملاحظات)
                    ))

            # 2. جلب رسوم التسجيل
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='registration_fees'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول registration_fees...")
                cursor.execute("""
                    SELECT payment_date, amount_paid, payment_type, notes, القسم
                    FROM registration_fees
                    WHERE payment_date BETWEEN ? AND ?
                    AND amount_paid > 0
                """, (start_date, end_date))

                registration_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد رسوم التسجيل: {len(registration_results)}")

                for row in registration_results:
                    # تجميع الملاحظات مع القسم
                    notes_with_section = []
                    if row[4]:  # القسم
                        notes_with_section.append(f"القسم: {row[4]}")
                    if row[3]:  # notes
                        notes_with_section.append(row[3])

                    combined_notes = " | ".join(notes_with_section) if notes_with_section else ""

                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # payment_date
                        'رسوم تسجيل',
                        f"رسوم تسجيل - {row[2]}",  # payment_type
                        float(row[1]) if row[1] else 0.0,  # amount_paid
                        0,
                        combined_notes  # ملاحظات مع القسم
                    ))

            # 3. جلب المصاريف
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='المصاريف'
            """)
            if cursor.fetchone():
                print("🔍 [DEBUG] معالجة جدول المصاريف...")
                cursor.execute("""
                    SELECT التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة, ملاحظات
                    FROM المصاريف
                    WHERE التاريخ BETWEEN ? AND ?
                """, (start_date, end_date))

                expenses_results = cursor.fetchall()
                print(f"🔍 [DEBUG] عدد المصاريف: {len(expenses_results)}")

                for row in expenses_results:
                    cursor.execute("""
                        INSERT INTO الحساب_الرئيسي
                        (تاريخ_العملية, نوع_العملية, بيان_العملية, الدخول, الخروج, ملاحظات)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # التاريخ
                        row[2] or 'مصروف عام',  # نوع_المصروف
                        f"{row[2]} - {row[3]}" if row[3] else row[2],  # بيان مع الجهة المستفيدة
                        0,
                        float(row[1]) if row[1] else 0.0,  # المبلغ
                        row[4] or ''  # ملاحظات
                    ))

            conn.commit()
            conn.close()
            print("✅ [SUCCESS] تم تجميع البيانات في الجدول الرئيسي بنجاح")
            return True

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تجميع البيانات: {str(e)}")
            return False

    def get_main_account_data(self, start_date, end_date):
        """جلب البيانات من جدول الحساب الرئيسي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT تاريخ_العملية, نوع_العملية, بيان_العملية,
                       الدخول, الخروج, ملاحظات
                FROM الحساب_الرئيسي
                WHERE تاريخ_العملية BETWEEN ? AND ?
                ORDER BY تاريخ_العملية DESC
            """, (start_date, end_date))

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    'date': row[0],
                    'type': row[1],
                    'description': row[2],
                    'inflow': float(row[3]) if row[3] else 0.0,
                    'outflow': float(row[4]) if row[4] else 0.0,
                    'notes': row[5] or ''
                }
                for row in results
            ]

        except Exception as e:
            print(f"❌ [ERROR] خطأ في جلب بيانات الحساب الرئيسي: {str(e)}")
            return []



class CashFlowWindow(QMainWindow):
    """نافذة تقرير التدفقات النقدية"""
    
    def __init__(self):
        super().__init__()
        self.manager = CashFlowManager()
        self.init_ui()
        self.load_cash_flow_data()
        # تحديث رصيد الصندوق عند بدء التشغيل
        self.update_cash_balance()
        # تحميل بيانات المسحوبات عند بدء التشغيل
        self.load_withdrawals_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💸 تقرير التدفقات النقدية (Cash Flow)")
        self.setWindowState(Qt.WindowMaximized)  # فتح في كامل الشاشة مباشرة
        
        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #87ceeb;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # شريط العنوان مع الأزرار
        self.create_title_bar_with_buttons(main_layout)
        
        # منطقة عرض التقرير
        self.create_report_area(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام التدفقات النقدية")
    
    def create_title_bar_with_buttons(self, main_layout):
        """إنشاء شريط العنوان مع جميع الأزرار"""
        # العنوان الرئيسي مع الأزرار
        title_group = QGroupBox()
        title_layout = QVBoxLayout(title_group)
        
        # العنوان
        title_label = QLabel("💸 تقرير التدفقات النقدية (Cash Flow)")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_layout.addWidget(title_label)
        
        # شريط الأزرار والتحكم
        controls_layout = QHBoxLayout()
        

        
        # تاريخ البداية
        start_label = QLabel("من تاريخ:")
        start_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(start_label)

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.start_date.setFixedSize(150, 30)
        self.start_date.setCalendarPopup(True)
        controls_layout.addWidget(self.start_date)

        # تاريخ النهاية
        end_label = QLabel("إلى تاريخ:")
        end_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(end_label)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFont(QFont("Calibri", 12, QFont.Bold))
        self.end_date.setFixedSize(150, 30)
        self.end_date.setCalendarPopup(True)
        controls_layout.addWidget(self.end_date)
        
        # فاصل
        controls_layout.addWidget(QLabel(""))
        
        # زر تحديث التقرير
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setFixedSize(150, 30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_cash_flow_data)
        controls_layout.addWidget(refresh_btn)

        # زر طباعة التقرير
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setFixedSize(150, 30)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        controls_layout.addWidget(print_btn)

        # فاصل
        controls_layout.addWidget(QLabel(""))

        # مربع رصيد الصندوق
        balance_label = QLabel("رصيد الصندوق:")
        balance_label.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(balance_label)

        self.balance_box = QLineEdit()
        self.balance_box.setFont(QFont("Calibri", 16, QFont.Bold))
        self.balance_box.setFixedSize(120, 60)
        self.balance_box.setReadOnly(True)  # غير مفعل
        self.balance_box.setAlignment(Qt.AlignCenter)
        self.balance_box.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #28a745;
                border-radius: 8px;
                color: #28a745;
                font-weight: bold;
                padding: 5px;
            }
        """)
        self.balance_box.setPlaceholderText("0.00")
        controls_layout.addWidget(self.balance_box)

        title_layout.addLayout(controls_layout)
        main_layout.addWidget(title_group)



    def create_report_area(self, main_layout):
        """إنشاء منطقة عرض التقرير مع جدول واحد"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 14, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 6px 20px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب الحساب الرئيسي
        self.create_main_account_tab()

        # تبويب ملخصات التدفقات النقدية
        self.create_summary_tab()

        # تبويب السحب من الصندوق
        self.create_withdrawal_tab()

        main_layout.addWidget(self.tab_widget)

    def create_main_account_tab(self):
        """إنشاء تبويب الحساب الرئيسي"""
        main_account_tab = QWidget()
        main_account_layout = QVBoxLayout(main_account_tab)

        # جدول الحساب الرئيسي
        self.main_account_table = QTableWidget()
        self.main_account_table.setColumnCount(6)
        self.main_account_table.setHorizontalHeaderLabels([
            "تاريخ العملية", "نوع العملية", "بيان العملية", "الدخول", "الخروج", "ملاحظات"
        ])

        # تنسيق جدول الحساب الرئيسي
        main_header = self.main_account_table.horizontalHeader()
        main_header.setFont(QFont("Calibri", 13, QFont.Bold))
        main_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 6px;
                border: 1px solid #e67e22;
                font-weight: bold;
                height: 30px;
                max-height: 30px;
                min-height: 30px;
            }
        """)

        self.main_account_table.setColumnWidth(0, 120)  # تاريخ العملية
        self.main_account_table.setColumnWidth(1, 150)  # نوع العملية
        self.main_account_table.setColumnWidth(2, 250)  # بيان العملية
        self.main_account_table.setColumnWidth(3, 120)  # الدخول
        self.main_account_table.setColumnWidth(4, 120)  # الخروج
        self.main_account_table.setColumnWidth(5, 300)  # ملاحظات

        self.main_account_table.setAlternatingRowColors(True)
        self.main_account_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.main_account_table.setFont(QFont("Calibri", 12))

        main_account_layout.addWidget(self.main_account_table)

        # إضافة التبويب
        self.tab_widget.addTab(main_account_tab, "التدفقات النقدية")

    def create_summary_tab(self):
        """إنشاء تبويب ملخصات التدفقات النقدية"""
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)

        # عنوان الملخص
        summary_title = QLabel("📊 ملخص التدفقات النقدية")
        summary_title.setFont(QFont("Calibri", 18, QFont.Bold))
        summary_title.setAlignment(Qt.AlignCenter)
        summary_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        summary_layout.addWidget(summary_title)

        # إنشاء البطاقات
        self.create_summary_cards(summary_layout)

        # إضافة التبويب
        self.tab_widget.addTab(summary_tab, "📊 ملخصات التدفقات النقدية")

    def create_withdrawal_tab(self):
        """إنشاء تبويب السحب من الصندوق"""
        withdrawal_tab = QWidget()
        withdrawal_layout = QVBoxLayout(withdrawal_tab)

        # منطقة إدخال البيانات
        input_group = QGroupBox("إدخال بيانات السحب")
        input_layout = QHBoxLayout(input_group)

        # مربع نص المبلغ المسحوب
        amount_label = QLabel("المبلغ المسحوب:")
        amount_label.setFont(QFont("Calibri", 14, QFont.Bold))
        input_layout.addWidget(amount_label)

        self.withdrawal_amount = QLineEdit()
        self.withdrawal_amount.setFont(QFont("Calibri", 16, QFont.Bold))
        self.withdrawal_amount.setFixedSize(100, 40)
        self.withdrawal_amount.setPlaceholderText("0.00")
        self.withdrawal_amount.setStyleSheet("""
            QLineEdit {
                border: 2px solid #3498db;
                border-radius: 6px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #2980b9;
            }
        """)
        input_layout.addWidget(self.withdrawal_amount)

        # مربع نص البيان
        description_label = QLabel("البيان:")
        description_label.setFont(QFont("Calibri", 14, QFont.Bold))
        input_layout.addWidget(description_label)

        self.withdrawal_description = QLineEdit()
        self.withdrawal_description.setFont(QFont("Calibri", 16, QFont.Bold))
        self.withdrawal_description.setFixedSize(350, 40)
        self.withdrawal_description.setPlaceholderText("بيان السحب...")
        self.withdrawal_description.setStyleSheet("""
            QLineEdit {
                border: 2px solid #3498db;
                border-radius: 6px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #2980b9;
            }
        """)
        input_layout.addWidget(self.withdrawal_description)

        # زر تسجيل السحب
        save_withdrawal_btn = QPushButton("💾 تسجيل السحب")
        save_withdrawal_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        save_withdrawal_btn.setFixedSize(150, 40)
        save_withdrawal_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        save_withdrawal_btn.clicked.connect(self.save_withdrawal)
        input_layout.addWidget(save_withdrawal_btn)

        input_layout.addStretch()
        withdrawal_layout.addWidget(input_group)

        # جدول المسحوبات
        withdrawals_group = QGroupBox("سجل المسحوبات")
        withdrawals_layout = QVBoxLayout(withdrawals_group)

        self.withdrawals_table = QTableWidget()
        self.withdrawals_table.setColumnCount(3)
        self.withdrawals_table.setHorizontalHeaderLabels([
            "تاريخ العملية", "المبلغ المسحوب", "البيان"
        ])

        # تنسيق جدول المسحوبات
        withdrawals_header = self.withdrawals_table.horizontalHeader()
        withdrawals_header.setFont(QFont("Calibri", 13, QFont.Bold))
        withdrawals_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #e74c3c;
                color: white;
                padding: 6px;
                border: 1px solid #c0392b;
                font-weight: bold;
                height: 30px;
            }
        """)

        self.withdrawals_table.setColumnWidth(0, 150)  # تاريخ العملية
        self.withdrawals_table.setColumnWidth(1, 150)  # المبلغ المسحوب
        self.withdrawals_table.setColumnWidth(2, 300)  # البيان

        self.withdrawals_table.setAlternatingRowColors(True)
        self.withdrawals_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.withdrawals_table.setFont(QFont("Calibri", 12))

        withdrawals_layout.addWidget(self.withdrawals_table)
        withdrawal_layout.addWidget(withdrawals_group)

        # إضافة التبويب
        self.tab_widget.addTab(withdrawal_tab, "💰 سحب من الصندوق")

    def create_summary_cards(self, layout):
        """إنشاء بطاقات ملخص التدفقات النقدية"""
        # تخطيط رئيسي للبطاقات
        cards_layout = QVBoxLayout()

        # الصف الأول: ثلاث بطاقات متقابلة
        top_row = QHBoxLayout()

        # بطاقة التدفقات الداخلة
        self.inflows_card = self.create_inflows_card()
        top_row.addWidget(self.inflows_card)

        # بطاقة التدفقات الخارجة
        self.outflows_card = self.create_outflows_card()
        top_row.addWidget(self.outflows_card)

        # بطاقة صافي التدفقات النقدية
        self.net_flow_card = self.create_net_flow_card()
        top_row.addWidget(self.net_flow_card)

        cards_layout.addLayout(top_row)

        layout.addLayout(cards_layout)

    def create_inflows_card(self):
        """إنشاء بطاقة التدفقات الداخلة"""
        card = QFrame()
        card.setFixedSize(350, 300)
        card.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 5px;
            }
        """)

        layout = QVBoxLayout(card)

        # عنوان البطاقة
        title = QLabel("💰 التدفقات الداخلة")
        title.setFont(QFont("Calibri", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #27ae60; margin-bottom: 5px;")
        layout.addWidget(title)

        # منطقة المحتوى
        self.inflows_content = QLabel("لا توجد بيانات")
        self.inflows_content.setFont(QFont("Calibri", 12, QFont.Bold))
        self.inflows_content.setAlignment(Qt.AlignTop)
        self.inflows_content.setWordWrap(True)
        self.inflows_content.setStyleSheet("color: #2c3e50; padding: 0px;")
        layout.addWidget(self.inflows_content)

        return card

    def create_outflows_card(self):
        """إنشاء بطاقة التدفقات الخارجة"""
        card = QFrame()
        card.setFixedSize(350, 300)
        card.setStyleSheet("""
            QFrame {
                background-color: #fde8e8;
                border: 2px solid #e74c3c;
                border-radius: 15px;
                padding: 5px;
            }
        """)

        layout = QVBoxLayout(card)

        # عنوان البطاقة
        title = QLabel("💸 التدفقات الخارجة")
        title.setFont(QFont("Calibri", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #e74c3c; margin-bottom: 5px;")
        layout.addWidget(title)

        # مجموع التدفقات الخارجة
        self.outflows_total = QLabel("إجمالي: 0.00 درهم")
        self.outflows_total.setFont(QFont("Calibri", 12, QFont.Bold))
        self.outflows_total.setAlignment(Qt.AlignCenter)
        self.outflows_total.setStyleSheet("color: #e74c3c; margin-bottom: 5px;")
        layout.addWidget(self.outflows_total)

        # منطقة المحتوى - 8 بطاقات في عمودين (4 في كل عمود)
        content_layout = QGridLayout()
        content_layout.setSpacing(2)
        self.outflows_content_widgets = []

        # إنشاء 8 بطاقات في عمودين (4 في كل عمود)
        for i in range(4):  # 4 صفوف
            for j in range(2):  # عمودين
                label = QLabel("")
                label.setFixedSize(140, 35)
                label.setFont(QFont("Calibri", 12, QFont.Bold))
                label.setAlignment(Qt.AlignCenter)
                label.setStyleSheet("""
                    QLabel {
                        color: #2c3e50;
                        background-color: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 8px;
                        padding: 0px;
                        margin: 1px;
                    }
                """)
                content_layout.addWidget(label, i, j)
                self.outflows_content_widgets.append(label)

        layout.addLayout(content_layout)

        return card

    def create_net_flow_card(self):
        """إنشاء بطاقة النتيجة"""
        card = QFrame()
        card.setFixedSize(430, 300)
        card.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 5px;
            }
        """)

        layout = QVBoxLayout(card)

        # بطاقة مجموع التدفقات الداخلية
        inflows_summary = QFrame()
        inflows_summary.setFixedSize(400, 70)
        inflows_summary.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin: 2px;
            }
        """)
        inflows_layout = QHBoxLayout(inflows_summary)
        inflows_layout.setContentsMargins(5, 0, 5, 0)

        inflows_title = QLabel("💰 مجموع التدفقات الداخلية:")
        inflows_title.setFont(QFont("Calibri", 12, QFont.Bold))
        inflows_title.setStyleSheet("color: #27ae60;")
        inflows_layout.addWidget(inflows_title)

        self.total_inflows_amount = QLabel("0.00 درهم")
        self.total_inflows_amount.setFont(QFont("Calibri", 12, QFont.Bold))
        self.total_inflows_amount.setAlignment(Qt.AlignRight)
        self.total_inflows_amount.setStyleSheet("color: #27ae60;")
        inflows_layout.addWidget(self.total_inflows_amount)

        layout.addWidget(inflows_summary)

        # بطاقة مجموع التدفقات الخارجية
        outflows_summary = QFrame()
        outflows_summary.setFixedSize(400, 70)
        outflows_summary.setStyleSheet("""
            QFrame {
                background-color: #fde8e8;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin: 2px;
            }
        """)
        outflows_layout = QHBoxLayout(outflows_summary)
        outflows_layout.setContentsMargins(5, 0, 5, 0)

        outflows_title = QLabel("💸 مجموع التدفقات الخارجية:")
        outflows_title.setFont(QFont("Calibri", 12, QFont.Bold))
        outflows_title.setStyleSheet("color: #e74c3c;")
        outflows_layout.addWidget(outflows_title)

        self.total_outflows_amount = QLabel("0.00 درهم")
        self.total_outflows_amount.setFont(QFont("Calibri", 12, QFont.Bold))
        self.total_outflows_amount.setAlignment(Qt.AlignRight)
        self.total_outflows_amount.setStyleSheet("color: #e74c3c;")
        outflows_layout.addWidget(self.total_outflows_amount)

        layout.addWidget(outflows_summary)

        # بطاقة النتيجة
        result_summary = QFrame()
        result_summary.setFixedSize(400, 70)
        result_summary.setStyleSheet("""
            QFrame {
                background-color: #e3f2fd;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin: 2px;
            }
        """)
        result_layout = QHBoxLayout(result_summary)
        result_layout.setContentsMargins(5, 0, 5, 0)

        result_title = QLabel("📊 النتيجة:")
        result_title.setFont(QFont("Calibri", 12, QFont.Bold))
        result_title.setStyleSheet("color: #3498db;")
        result_layout.addWidget(result_title)

        self.net_flow_amount = QLabel("0.00 درهم")
        self.net_flow_amount.setFont(QFont("Calibri", 12, QFont.Bold))
        self.net_flow_amount.setAlignment(Qt.AlignRight)
        self.net_flow_amount.setStyleSheet("color: #2c3e50;")
        result_layout.addWidget(self.net_flow_amount)

        layout.addWidget(result_summary)

        return card










    def load_cash_flow_data(self):
        """تحميل بيانات التدفقات النقدية"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            print(f"🔍 [DEBUG] تحميل البيانات من {start_date} إلى {end_date}")

            # تجميع البيانات في الجدول الرئيسي
            if not self.manager.populate_main_account_table(start_date, end_date):
                QMessageBox.critical(self, "خطأ", "فشل في تجميع البيانات في الجدول الرئيسي")
                return

            # جلب البيانات من الجدول الرئيسي
            main_account_data = self.manager.get_main_account_data(start_date, end_date)
            print(f"🔍 [DEBUG] عدد العمليات في الحساب الرئيسي: {len(main_account_data)}")

            # تحديث الجدول
            self.update_main_account_table(main_account_data)

            # إنشاء ملخص التدفقات
            self.generate_cash_flow_summary_from_main_account(main_account_data, start_date, end_date)

            # تحديث رصيد الصندوق
            self.update_cash_balance()

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل التدفقات النقدية من {start_date} إلى {end_date} - عدد العمليات: {len(main_account_data)}")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التدفقات النقدية: {str(e)}")

    def update_main_account_table(self, main_account_data):
        """تحديث جدول الحساب الرئيسي"""
        self.main_account_table.setRowCount(len(main_account_data))

        for row, operation in enumerate(main_account_data):
            items = [
                operation['date'],
                operation['type'],
                operation['description'],
                f"{operation['inflow']:,.2f} درهم" if operation['inflow'] > 0 else "",
                f"{operation['outflow']:,.2f} درهم" if operation['outflow'] > 0 else "",
                operation['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 13, QFont.Bold))
                item.setForeground(QColor("#000000"))  # النص أسود غامق

                # تلوين المبالغ
                if col == 3 and operation['inflow'] > 0:  # الدخول
                    item.setForeground(QColor("#003366"))  # أزرق غامق
                    item.setFont(QFont("Calibri", 13, QFont.Bold))
                elif col == 4 and operation['outflow'] > 0:  # الخروج
                    item.setForeground(QColor("#990000"))  # أحمر غامق
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                self.main_account_table.setItem(row, col, item)

    def update_cash_balance(self):
        """تحديث رصيد الصندوق = مجموع الدخول - مجموع الخروج من جدول الحساب_الرئيسي - مجموع المسحوبات"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # إنشاء جدول المسحوبات إذا لم يكن موجوداً
            self.manager.create_withdrawals_table()

            # حساب مجموع الدخول
            cursor.execute("SELECT SUM(الدخول) FROM الحساب_الرئيسي")
            total_inflow = cursor.fetchone()[0] or 0.0

            # حساب مجموع الخروج
            cursor.execute("SELECT SUM(الخروج) FROM الحساب_الرئيسي")
            total_outflow = cursor.fetchone()[0] or 0.0

            # حساب مجموع المسحوبات
            cursor.execute("SELECT SUM(المبلغ_المسحوب) FROM المسحوبات")
            total_withdrawals = cursor.fetchone()[0] or 0.0

            # حساب الرصيد
            balance = total_inflow - total_outflow - total_withdrawals

            # تحديث مربع الرصيد
            self.balance_box.setText(f"{balance:,.2f}")

            # تغيير لون المربع حسب الرصيد
            if balance > 0:
                color = "#28a745"  # أخضر للرصيد الموجب
            elif balance < 0:
                color = "#dc3545"  # أحمر للرصيد السالب
            else:
                color = "#6c757d"  # رمادي للرصيد صفر

            self.balance_box.setStyleSheet(f"""
                QLineEdit {{
                    background-color: #f8f9fa;
                    border: 2px solid {color};
                    border-radius: 8px;
                    color: {color};
                    font-weight: bold;
                    padding: 5px;
                }}
            """)

            conn.close()
            print(f"✅ تم تحديث رصيد الصندوق: {balance:,.2f} درهم")

        except Exception as e:
            print(f"❌ خطأ في حساب رصيد الصندوق: {str(e)}")
            self.balance_box.setText("خطأ")

    def save_withdrawal(self):
        """حفظ عملية السحب في جدول المسحوبات"""
        try:
            # التحقق من صحة البيانات
            amount_text = self.withdrawal_amount.text().strip()
            description_text = self.withdrawal_description.text().strip()

            if not amount_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ المسحوب")
                return

            if not description_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال بيان السحب")
                return

            try:
                amount = float(amount_text)
                if amount <= 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return

            # إنشاء جدول المسحوبات إذا لم يكن موجوداً
            self.manager.create_withdrawals_table()

            # حفظ البيانات في قاعدة البيانات
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            current_date = datetime.now().strftime("%Y-%m-%d")

            cursor.execute("""
                INSERT INTO المسحوبات (تاريخ_العملية, المبلغ_المسحوب, البيان)
                VALUES (?, ?, ?)
            """, (current_date, amount, description_text))

            conn.commit()
            conn.close()

            # مسح الحقول
            self.withdrawal_amount.clear()
            self.withdrawal_description.clear()

            # تحديث جدول المسحوبات
            self.load_withdrawals_data()

            # تحديث رصيد الصندوق
            self.update_cash_balance()

            QMessageBox.information(self, "نجح", f"تم تسجيل السحب بنجاح\nالمبلغ: {amount:,.2f} درهم")

        except Exception as e:
            print(f"❌ خطأ في حفظ السحب: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تسجيل السحب: {str(e)}")

    def load_withdrawals_data(self):
        """تحميل بيانات المسحوبات في الجدول"""
        try:
            # إنشاء جدول المسحوبات إذا لم يكن موجوداً
            self.manager.create_withdrawals_table()

            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            cursor.execute("""
                SELECT تاريخ_العملية, المبلغ_المسحوب, البيان
                FROM المسحوبات
                ORDER BY تاريخ_العملية DESC
            """)

            withdrawals = cursor.fetchall()
            conn.close()

            # تحديث الجدول
            self.withdrawals_table.setRowCount(len(withdrawals))

            for row, withdrawal in enumerate(withdrawals):
                # تاريخ العملية
                date_item = QTableWidgetItem(str(withdrawal[0]))
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setFont(QFont("Calibri", 12, QFont.Bold))
                self.withdrawals_table.setItem(row, 0, date_item)

                # المبلغ المسحوب
                amount_item = QTableWidgetItem(f"{withdrawal[1]:,.2f} درهم")
                amount_item.setTextAlignment(Qt.AlignCenter)
                amount_item.setFont(QFont("Calibri", 12, QFont.Bold))
                amount_item.setForeground(QColor("#e74c3c"))  # أحمر
                self.withdrawals_table.setItem(row, 1, amount_item)

                # البيان
                description_item = QTableWidgetItem(str(withdrawal[2]))
                description_item.setTextAlignment(Qt.AlignCenter)
                description_item.setFont(QFont("Calibri", 12, QFont.Bold))
                self.withdrawals_table.setItem(row, 2, description_item)

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات المسحوبات: {str(e)}")

    def generate_cash_flow_summary_from_main_account(self, main_account_data, start_date, end_date):
        """إنشاء ملخص التدفقات النقدية من الجدول الرئيسي"""
        try:
            # حساب الإجماليات
            total_inflows = sum(operation['inflow'] for operation in main_account_data)
            total_outflows = sum(operation['outflow'] for operation in main_account_data)
            net_cash_flow = total_inflows - total_outflows

            # تحديث بطاقة التدفقات الداخلة
            inflow_types = {}
            for operation in main_account_data:
                if operation['inflow'] > 0:
                    op_type = operation['type']
                    if op_type not in inflow_types:
                        inflow_types[op_type] = 0
                    inflow_types[op_type] += operation['inflow']

            inflows_text = f"إجمالي: {total_inflows:,.2f} درهم\n\n"
            if inflow_types:
                for op_type, amount in inflow_types.items():
                    inflows_text += f"• {op_type}: {amount:,.2f} درهم\n"
            else:
                inflows_text += "لا توجد تدفقات داخلة"

            self.inflows_content.setText(inflows_text)

            # تحديث بطاقة التدفقات الخارجة
            outflow_types = {}
            for operation in main_account_data:
                if operation['outflow'] > 0:
                    op_type = operation['type']
                    if op_type not in outflow_types:
                        outflow_types[op_type] = 0
                    outflow_types[op_type] += operation['outflow']

            # تحديث مجموع التدفقات الخارجة
            self.outflows_total.setText(f"إجمالي: {total_outflows:,.2f} درهم")

            # تحديث عناصر التدفقات الخارجة في عمودين
            outflow_items = list(outflow_types.items())
            for i, widget in enumerate(self.outflows_content_widgets):
                if i < len(outflow_items):
                    op_type, amount = outflow_items[i]
                    widget.setText(f"{op_type}: {amount:,.0f}")
                else:
                    widget.setText("")

            # تحديث البطاقات الثلاث
            # بطاقة مجموع التدفقات الداخلية
            self.total_inflows_amount.setText(f"{total_inflows:,.2f} درهم")

            # بطاقة مجموع التدفقات الخارجية
            self.total_outflows_amount.setText(f"{total_outflows:,.2f} درهم")

            # بطاقة النتيجة
            self.net_flow_amount.setText(f"{net_cash_flow:,.2f} درهم")

            if net_cash_flow > 0:
                self.net_flow_amount.setStyleSheet("color: #27ae60;")
            elif net_cash_flow < 0:
                self.net_flow_amount.setStyleSheet("color: #e74c3c;")
            else:
                self.net_flow_amount.setStyleSheet("color: #3498db;")

        except Exception as e:
            print(f"❌ [ERROR] خطأ في إنشاء ملخص التدفقات: {str(e)}")

    def print_report(self):
        """عرض قائمة اختيار نوع التقرير"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

        # إنشاء نافذة الاختيار
        dialog = QDialog(self)
        dialog.setWindowTitle("اختيار نوع التقرير")
        dialog.setFixedSize(600, 300)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 10px;
            }
        """)

        layout = QVBoxLayout(dialog)

        # عنوان النافذة
        title_label = QLabel("اختر نوع التقرير المطلوب:")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)

        # أزرار الاختيار
        buttons_layout = QHBoxLayout()

        # زر التقرير الإجمالي
        summary_btn = QPushButton("📊 التقرير الإجمالي")
        summary_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        summary_btn.setFixedSize(150, 40)
        summary_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        summary_btn.clicked.connect(lambda: self.generate_summary_report(dialog))
        buttons_layout.addWidget(summary_btn)

        # زر التقرير التفصيلي
        detailed_btn = QPushButton("📋 التقرير التفصيلي")
        detailed_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        detailed_btn.setFixedSize(150, 40)
        detailed_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        detailed_btn.clicked.connect(lambda: self.generate_detailed_report(dialog))
        buttons_layout.addWidget(detailed_btn)

        # زر تقرير المسحوبات تفصيلاً
        withdrawals_btn = QPushButton("💰 تقرير المسحوبات تفصيلاً")
        withdrawals_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        withdrawals_btn.setFixedSize(180, 40)
        withdrawals_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        withdrawals_btn.clicked.connect(lambda: self.generate_withdrawals_report(dialog))
        buttons_layout.addWidget(withdrawals_btn)

        layout.addLayout(buttons_layout)

        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFont(QFont("Calibri", 10))
        cancel_btn.setFixedSize(80, 30)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(dialog.reject)
        layout.addWidget(cancel_btn, alignment=Qt.AlignCenter)

        dialog.exec_()

    def generate_summary_report(self, dialog):
        """إنشاء التقرير الإجمالي"""
        dialog.accept()
        try:
            import os
            import sys
            import sqlite3
            import subprocess
            from datetime import datetime

            # تثبيت المكتبات المطلوبة
            try:
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display
            except ImportError:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display

            # إنشاء كلاس PDF للعربية
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('L', 'mm', 'A4')  # أفقي
                    self.set_margins(10, 10, 10)
                    self.set_auto_page_break(auto=True, margin=10)

                    # إضافة الخطوط
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                def ar_text(self, txt):
                    """تحويل النص العربي"""
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

            # إنشاء PDF
            pdf = ArabicPDF()
            pdf.add_page()

            y = 10
            page_width = 297 - 20  # عرض الصفحة الأفقية مطروح منه الهوامش

            # الشعار واسم المؤسسة
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
                institution_data = cursor.fetchone()
                conn.close()

                if institution_data:
                    logo_path, institution_name = institution_data
                    if logo_path and os.path.exists(logo_path):
                        pdf.image(logo_path, x=(page_width - 40) / 2 + 10, y=y, w=40, h=20)
                        y += 25

                    if institution_name:
                        if pdf.calibri_bold_available:
                            pdf.set_font('Calibri', 'B', 16)
                        else:
                            pdf.set_font('Helvetica', 'B', 16)
                        pdf.set_xy(10, y)
                        pdf.cell(page_width, 10, pdf.ar_text(institution_name), border=0, align='C')
                        y += 10
            except:
                # في حالة عدم وجود قاعدة البيانات
                if pdf.calibri_bold_available:
                    pdf.set_font('Calibri', 'B', 16)
                else:
                    pdf.set_font('Helvetica', 'B', 16)
                pdf.set_xy(10, y)
                pdf.cell(page_width, 10, pdf.ar_text("اسم المؤسسة"), border=0, align='C')
                y += 10

            # مسافة 2 نقطة
            y += 2

            # عنوان التقرير
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 14)
            else:
                pdf.set_font('Helvetica', 'B', 14)
            pdf.set_xy(10, y)
            pdf.cell(page_width, 10, pdf.ar_text("تقرير التدفقات النقدية"), border=0, align='C')
            y += 10

            # مسافة 2 نقطة
            y += 2

            # الفترة الزمنية
            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Helvetica', '', 12)
            period_text = f"الفترة: من {self.start_date.date().toString('yyyy-MM-dd')} إلى {self.end_date.date().toString('yyyy-MM-dd')}"
            pdf.set_xy(10, y)
            pdf.cell(page_width, 8, pdf.ar_text(period_text), border=0, align='C')
            y += 20

            # جدول التدفقات النقدية
            self.create_cash_flow_table(pdf, y, page_width)

            # التوقيع والتاريخ
            y = 190  # أسفل الصفحة
            current_date = datetime.now().strftime("%Y-%m-%d")

            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Helvetica', '', 12)

            pdf.set_xy(10, y)
            pdf.cell(page_width / 2, 8, pdf.ar_text(f"التاريخ: {current_date}"), border=0, align='L')

            pdf.set_xy(10 + page_width / 2, y)
            pdf.cell(page_width / 2, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')

            # حفظ وفتح الملف
            filename = f"تقرير_التدفقات_الإجمالي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf.output(filename)

            # فتح الملف مباشرة
            if os.name == 'nt':  # Windows
                os.startfile(filename)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', filename])

            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير الإجمالي بنجاح!\nاسم الملف: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير الإجمالي: {str(e)}")

    def generate_detailed_report(self, dialog):
        """إنشاء التقرير التفصيلي"""
        dialog.accept()
        try:
            import os
            import sys
            import sqlite3
            import subprocess
            from datetime import datetime

            # تثبيت المكتبات المطلوبة
            try:
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display
            except ImportError:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display

            # إنشاء كلاس PDF للعربية
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('L', 'mm', 'A4')  # أفقي
                    self.set_margins(10, 10, 10)
                    self.set_auto_page_break(auto=True, margin=10)

                    # إضافة الخطوط
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                def ar_text(self, txt):
                    """تحويل النص العربي"""
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

            # إنشاء PDF
            pdf = ArabicPDF()
            pdf.add_page()

            y = 10
            page_width = 297 - 20  # عرض الصفحة الأفقية مطروح منه الهوامش

            # الشعار واسم المؤسسة
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
                institution_data = cursor.fetchone()
                conn.close()

                if institution_data:
                    logo_path, institution_name = institution_data
                    if logo_path and os.path.exists(logo_path):
                        pdf.image(logo_path, x=(page_width - 40) / 2 + 10, y=y, w=40, h=20)
                        y += 25

                    if institution_name:
                        if pdf.calibri_bold_available:
                            pdf.set_font('Calibri', 'B', 16)
                        else:
                            pdf.set_font('Helvetica', 'B', 16)
                        pdf.set_xy(10, y)
                        pdf.cell(page_width, 10, pdf.ar_text(institution_name), border=0, align='C')
                        y += 10
            except:
                # في حالة عدم وجود قاعدة البيانات
                if pdf.calibri_bold_available:
                    pdf.set_font('Calibri', 'B', 16)
                else:
                    pdf.set_font('Helvetica', 'B', 16)
                pdf.set_xy(10, y)
                pdf.cell(page_width, 10, pdf.ar_text("اسم المؤسسة"), border=0, align='C')
                y += 10

            # مسافة 2 نقطة
            y += 2

            # عنوان التقرير
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 14)
            else:
                pdf.set_font('Arial', 'B', 14)
            pdf.set_xy(10, y)
            pdf.cell(page_width, 10, pdf.ar_text("التقرير التفصيلي للتدفقات النقدية"), border=0, align='C')
            y += 10

            # مسافة 2 نقطة
            y += 2

            # الفترة الزمنية
            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Arial', '', 12)
            period_text = f"الفترة: من {self.start_date.date().toString('yyyy-MM-dd')} إلى {self.end_date.date().toString('yyyy-MM-dd')}"
            pdf.set_xy(10, y)
            pdf.cell(page_width, 8, pdf.ar_text(period_text), border=0, align='C')
            y += 20

            # جدول التدفقات النقدية التفصيلي
            self.create_detailed_cash_flow_table(pdf, y, page_width)

            # التوقيع والتاريخ
            y = 190  # أسفل الصفحة
            current_date = datetime.now().strftime("%Y-%m-%d")

            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Arial', '', 12)

            pdf.set_xy(10, y)
            pdf.cell(page_width / 2, 8, pdf.ar_text(f"التاريخ: {current_date}"), border=0, align='L')

            pdf.set_xy(10 + page_width / 2, y)
            pdf.cell(page_width / 2, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')

            # حفظ وفتح الملف
            filename = f"تقرير_التدفقات_التفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf.output(filename)

            # فتح الملف مباشرة
            if os.name == 'nt':  # Windows
                os.startfile(filename)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', filename])

            QMessageBox.information(self, "نجح", f"تم إنشاء التقرير التفصيلي بنجاح!\nاسم الملف: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير التفصيلي: {str(e)}")

    def generate_withdrawals_report(self, dialog):
        """إنشاء تقرير المسحوبات تفصيلاً"""
        dialog.accept()
        try:
            import os
            import sys
            import sqlite3
            import subprocess
            from datetime import datetime

            # تثبيت المكتبات المطلوبة
            try:
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display
            except ImportError:
                # تثبيت المكتبات المطلوبة
                import subprocess
                subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display

            # إنشاء جدول المسحوبات إذا لم يكن موجوداً
            self.manager.create_withdrawals_table()

            # فئة PDF مخصصة للعربية
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__()
                    self.calibri_available = False
                    self.calibri_bold_available = False

                    # محاولة إضافة خط Calibri
                    try:
                        self.add_font('Calibri', '', 'calibri.ttf')
                        self.calibri_available = True
                        try:
                            self.add_font('Calibri', 'B', 'calibrib.ttf')
                            self.calibri_bold_available = True
                        except:
                            pass
                    except:
                        pass

                def ar_text(self, text):
                    """تحويل النص العربي للعرض الصحيح"""
                    try:
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        return get_display(reshaped_text)
                    except:
                        return str(text)

            # إنشاء PDF
            pdf = ArabicPDF()
            pdf.add_page()
            page_width = 190  # عرض الصفحة A4 مطروحاً منه الهوامش

            # جلب بيانات المؤسسة
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # جلب بيانات المؤسسة
            cursor.execute("SELECT المؤسسة, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()

            institution_name = institution_data[0] if institution_data else "اسم المؤسسة"
            logo_path = institution_data[1] if institution_data and institution_data[1] else None

            y = 20  # البداية من الأعلى

            # إضافة الشعار إذا كان متوفراً
            if logo_path and os.path.exists(logo_path):
                try:
                    pdf.image(logo_path, 10, y, 30, 30)
                    y += 35
                except:
                    y += 10
            else:
                y += 10

            # اسم المؤسسة
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 16)
            else:
                pdf.set_font('Helvetica', 'B', 16)
            pdf.set_xy(10, y)
            pdf.cell(page_width, 10, pdf.ar_text(institution_name), border=0, align='C')
            y += 15

            # مسافة 2 نقطة
            y += 2

            # عنوان التقرير
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 14)
            else:
                pdf.set_font('Arial', 'B', 14)
            pdf.set_xy(10, y)
            pdf.cell(page_width, 10, pdf.ar_text("تقرير المسحوبات تفصيلاً"), border=0, align='C')
            y += 15

            # مسافة 2 نقطة
            y += 2

            # الفترة الزمنية
            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Arial', '', 12)
            current_date = datetime.now().strftime("%Y-%m-%d")
            period_text = f"تاريخ التقرير: {current_date}"
            pdf.set_xy(10, y)
            pdf.cell(page_width, 8, pdf.ar_text(period_text), border=0, align='C')
            y += 20

            # جدول المسحوبات التفصيلي
            self.create_withdrawals_table(pdf, y, page_width)

            # التوقيع والتاريخ
            y = 190  # أسفل الصفحة
            current_date = datetime.now().strftime("%Y-%m-%d")

            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Arial', '', 12)

            pdf.set_xy(10, y)
            pdf.cell(page_width / 2, 8, pdf.ar_text(f"التاريخ: {current_date}"), border=0, align='L')

            pdf.set_xy(10 + page_width / 2, y)
            pdf.cell(page_width / 2, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')

            # حفظ وفتح الملف
            filename = f"تقرير_المسحوبات_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            pdf.output(filename)

            # فتح الملف مباشرة
            if os.name == 'nt':  # Windows
                os.startfile(filename)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', filename])

            QMessageBox.information(self, "نجح", f"تم إنشاء تقرير المسحوبات بنجاح!\nاسم الملف: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير المسحوبات: {str(e)}")

    def create_withdrawals_table(self, pdf, y, page_width):
        """إنشاء جدول المسحوبات في PDF"""
        try:
            import sqlite3

            # جلب البيانات من جدول المسحوبات
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # جلب جميع البيانات من جدول المسحوبات
            cursor.execute("""
                SELECT
                    تاريخ_العملية,
                    المبلغ_المسحوب,
                    البيان
                FROM المسحوبات
                ORDER BY تاريخ_العملية DESC
            """)

            withdrawals_data = cursor.fetchall()
            conn.close()

            # إعداد الجدول - خط رؤوس الجدول
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 15)
            else:
                pdf.set_font('Arial', 'B', 15)

            # عرض الأعمدة
            col_widths = [
                page_width * 0.25,  # تاريخ العملية
                page_width * 0.25,  # المبلغ المسحوب
                page_width * 0.50   # البيان
            ]
            row_height = 8

            # رأس الجدول
            headers = ["تاريخ العملية", "المبلغ المسحوب", "البيان"]
            x = 10

            for i, header in enumerate(headers):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], row_height, pdf.ar_text(header), border=1, align='C')
                x += col_widths[i]

            y += row_height

            # تغيير الخط للبيانات
            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 12)
            else:
                pdf.set_font('Arial', '', 12)

            # عرض البيانات
            total_withdrawals = 0
            for withdrawal in withdrawals_data:
                x = 10

                # تاريخ العملية
                pdf.set_xy(x, y)
                pdf.set_text_color(0, 0, 0)  # أسود
                pdf.cell(col_widths[0], row_height, str(withdrawal[0]), border=1, align='C')
                x += col_widths[0]

                # المبلغ المسحوب
                pdf.set_xy(x, y)
                pdf.set_text_color(220, 53, 69)  # أحمر
                amount_text = f"{float(withdrawal[1]):,.2f} درهم"
                pdf.cell(col_widths[1], row_height, amount_text, border=1, align='C')
                x += col_widths[1]
                total_withdrawals += float(withdrawal[1])

                # البيان
                pdf.set_xy(x, y)
                pdf.set_text_color(0, 0, 0)  # أسود
                pdf.cell(col_widths[2], row_height, pdf.ar_text(str(withdrawal[2])), border=1, align='C')

                y += row_height

            # إضافة صف الإجمالي
            y += 5
            x = 10

            # خلية فارغة
            pdf.set_xy(x, y)
            pdf.set_text_color(0, 0, 0)
            pdf.set_font('Calibri', 'B', 12) if pdf.calibri_bold_available else pdf.set_font('Arial', 'B', 12)
            pdf.cell(col_widths[0], row_height, "", border=1, align='C')
            x += col_widths[0]

            # إجمالي المسحوبات
            pdf.set_xy(x, y)
            pdf.set_text_color(220, 53, 69)  # أحمر
            total_text = f"الإجمالي: {total_withdrawals:,.2f} درهم"
            pdf.cell(col_widths[1], row_height, total_text, border=1, align='C')
            x += col_widths[1]

            # خلية فارغة
            pdf.set_xy(x, y)
            pdf.set_text_color(0, 0, 0)
            pdf.cell(col_widths[2], row_height, "", border=1, align='C')

            # إعادة تعيين لون النص
            pdf.set_text_color(0, 0, 0)

        except Exception as e:
            print(f"خطأ في إنشاء جدول المسحوبات: {str(e)}")

    def create_detailed_cash_flow_table(self, pdf, y, page_width):
        """إنشاء جدول التدفقات النقدية التفصيلي في PDF"""
        try:
            import sqlite3

            # جلب البيانات التفصيلية من جدول الحساب_الرئيسي
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # الحصول على الفترة الزمنية
            start_date = self.start_date.date().toString('yyyy-MM-dd')
            end_date = self.end_date.date().toString('yyyy-MM-dd')

            # جلب جميع البيانات التفصيلية
            cursor.execute("""
                SELECT
                    تاريخ_العملية,
                    نوع_العملية,
                    بيان_العملية,
                    الدخول,
                    الخروج,
                    ملاحظات
                FROM الحساب_الرئيسي
                WHERE تاريخ_العملية BETWEEN ? AND ?
                ORDER BY تاريخ_العملية, نوع_العملية
            """, (start_date, end_date))

            operations_data = cursor.fetchall()
            conn.close()

            # إعداد الجدول - خط رؤوس الجدول
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 15)
            else:
                pdf.set_font('Arial', 'B', 15)

            # عرض الأعمدة
            col_widths = [
                page_width * 0.12,  # تاريخ العملية
                page_width * 0.18,  # نوع العملية
                page_width * 0.25,  # بيان العملية
                page_width * 0.15,  # الدخول
                page_width * 0.15,  # الخروج
                page_width * 0.15   # ملاحظات
            ]
            row_height = 8

            # رأس الجدول
            headers = ["تاريخ العملية", "نوع العملية", "بيان العملية", "الدخول", "الخروج", "ملاحظات"]
            x = 10

            for i, header in enumerate(headers):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], row_height, pdf.ar_text(header), border=1, align='C')
                x += col_widths[i]

            y += row_height

            # صفوف البيانات - خط صفوف الجدول
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)
            else:
                pdf.set_font('Arial', 'B', 13)

            # عرض البيانات التفصيلية
            for operation in operations_data:
                x = 10

                # تاريخ العملية
                pdf.set_xy(x, y)
                pdf.set_text_color(0, 0, 0)  # أسود
                pdf.cell(col_widths[0], row_height, str(operation[0]), border=1, align='C')
                x += col_widths[0]

                # نوع العملية
                pdf.set_xy(x, y)
                pdf.cell(col_widths[1], row_height, pdf.ar_text(str(operation[1])), border=1, align='C')
                x += col_widths[1]

                # بيان العملية
                pdf.set_xy(x, y)
                pdf.cell(col_widths[2], row_height, pdf.ar_text(str(operation[2])), border=1, align='C')
                x += col_widths[2]

                # الدخول
                pdf.set_xy(x, y)
                if operation[3] > 0:
                    # مبلغ الدخول بأزرق غامق وخط Calibri 14 Bold
                    if pdf.calibri_bold_available:
                        pdf.set_font('Calibri', 'B', 14)
                    else:
                        pdf.set_font('Arial', 'B', 14)
                    pdf.set_text_color(0, 0, 139)  # أزرق غامق
                    pdf.cell(col_widths[3], row_height, f"{operation[3]:,.2f}", border=1, align='C')
                else:
                    pdf.cell(col_widths[3], row_height, "", border=1, align='C')
                x += col_widths[3]

                # الخروج
                pdf.set_xy(x, y)
                if operation[4] > 0:
                    # مبلغ الخروج بأحمر غامق وخط Calibri 14 Bold
                    if pdf.calibri_bold_available:
                        pdf.set_font('Calibri', 'B', 14)
                    else:
                        pdf.set_font('Arial', 'B', 14)
                    pdf.set_text_color(139, 0, 0)  # أحمر غامق
                    pdf.cell(col_widths[4], row_height, f"{operation[4]:,.2f}", border=1, align='C')
                else:
                    pdf.cell(col_widths[4], row_height, "", border=1, align='C')
                x += col_widths[4]

                # إعادة تعيين الخط للملاحظات
                if pdf.calibri_bold_available:
                    pdf.set_font('Calibri', 'B', 13)
                else:
                    pdf.set_font('Arial', 'B', 13)
                pdf.set_text_color(0, 0, 0)  # أسود

                # ملاحظات
                pdf.set_xy(x, y)
                notes = str(operation[5]) if operation[5] else ""
                pdf.cell(col_widths[5], row_height, pdf.ar_text(notes), border=1, align='C')

                y += row_height

            # إعادة تعيين لون النص
            pdf.set_text_color(0, 0, 0)

        except Exception as e:
            print(f"خطأ في إنشاء الجدول التفصيلي: {str(e)}")

    def create_cash_flow_table(self, pdf, y, page_width):
        """إنشاء جدول التدفقات النقدية في PDF"""
        try:
            import sqlite3

            # جلب البيانات من جدول الحساب_الرئيسي
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # الحصول على الفترة الزمنية
            start_date = self.start_date.date().toString('yyyy-MM-dd')
            end_date = self.end_date.date().toString('yyyy-MM-dd')

            # جلب البيانات مجمعة حسب نوع_العملية
            cursor.execute("""
                SELECT
                    نوع_العملية,
                    SUM(الدخول) as total_inflow,
                    SUM(الخروج) as total_outflow
                FROM الحساب_الرئيسي
                WHERE تاريخ_العملية BETWEEN ? AND ?
                GROUP BY نوع_العملية
                ORDER BY نوع_العملية
            """, (start_date, end_date))

            operations_data = cursor.fetchall()
            conn.close()

            # حساب الإجماليات
            total_inflows = sum(row[1] for row in operations_data)
            total_outflows = sum(row[2] for row in operations_data)
            net_cash_flow = total_inflows - total_outflows

            # إعداد الجدول - خط رؤوس الجدول
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 15)
            else:
                pdf.set_font('Arial', 'B', 15)

            # عرض الأعمدة
            col_width = page_width / 4
            row_height = 8

            # رأس الجدول
            headers = ["المبلغ (درهم)", "نوع العملية", "التصنيف", "م"]
            x = 10

            for header in headers:
                pdf.set_xy(x, y)
                pdf.cell(col_width, row_height, pdf.ar_text(header), border=1, align='C')
                x += col_width

            y += row_height

            # صفوف البيانات - خط صفوف الجدول
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)
            else:
                pdf.set_font('Arial', 'B', 13)

            row_num = 1

            # عرض التدفقات الداخلة
            for operation_type, inflow, outflow in operations_data:
                if inflow > 0:
                    x = 10
                    pdf.set_xy(x, y)
                    # مبلغ الدخول بأزرق غامق وخط Calibri 14 Bold
                    if pdf.calibri_bold_available:
                        pdf.set_font('Calibri', 'B', 14)
                    else:
                        pdf.set_font('Arial', 'B', 14)
                    pdf.set_text_color(0, 0, 139)  # أزرق غامق
                    pdf.cell(col_width, row_height, f"{inflow:,.2f}", border=1, align='C')

                    # باقي الخلايا بخط صفوف الجدول العادي
                    if pdf.calibri_bold_available:
                        pdf.set_font('Calibri', 'B', 13)
                    else:
                        pdf.set_font('Arial', 'B', 13)
                    pdf.set_text_color(0, 0, 0)  # أسود

                    x += col_width
                    pdf.set_xy(x, y)
                    pdf.cell(col_width, row_height, pdf.ar_text(operation_type), border=1, align='C')
                    x += col_width
                    pdf.set_xy(x, y)
                    pdf.cell(col_width, row_height, pdf.ar_text("تدفق داخل"), border=1, align='C')
                    x += col_width
                    pdf.set_xy(x, y)
                    pdf.cell(col_width, row_height, str(row_num), border=1, align='C')

                    y += row_height
                    row_num += 1

            # عرض التدفقات الخارجة
            for operation_type, inflow, outflow in operations_data:
                if outflow > 0:
                    x = 10
                    pdf.set_xy(x, y)
                    # مبلغ الخروج بأحمر غامق وخط Calibri 14 Bold
                    if pdf.calibri_bold_available:
                        pdf.set_font('Calibri', 'B', 14)
                    else:
                        pdf.set_font('Arial', 'B', 14)
                    pdf.set_text_color(139, 0, 0)  # أحمر غامق
                    pdf.cell(col_width, row_height, f"{outflow:,.2f}", border=1, align='C')

                    # باقي الخلايا بخط صفوف الجدول العادي
                    if pdf.calibri_bold_available:
                        pdf.set_font('Calibri', 'B', 13)
                    else:
                        pdf.set_font('Arial', 'B', 13)
                    pdf.set_text_color(0, 0, 0)  # أسود

                    x += col_width
                    pdf.set_xy(x, y)
                    pdf.cell(col_width, row_height, pdf.ar_text(operation_type), border=1, align='C')
                    x += col_width
                    pdf.set_xy(x, y)
                    pdf.cell(col_width, row_height, pdf.ar_text("تدفق خارج"), border=1, align='C')
                    x += col_width
                    pdf.set_xy(x, y)
                    pdf.cell(col_width, row_height, str(row_num), border=1, align='C')

                    y += row_height
                    row_num += 1

            # خط فاصل
            pdf.line(10, y, 10 + page_width, y)
            y += 5

            # صفوف الإجماليات
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)
            else:
                pdf.set_font('Arial', 'B', 13)

            # إجمالي التدفقات الداخلة
            x = 10
            pdf.set_xy(x, y)
            # مبلغ الإجمالي بأزرق غامق وخط Calibri 14 Bold
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 14)
            else:
                pdf.set_font('Arial', 'B', 14)
            pdf.set_text_color(0, 0, 139)  # أزرق غامق
            pdf.cell(col_width, row_height, f"{total_inflows:,.2f}", border=1, align='C')

            # باقي الخلايا بخط صفوف الجدول العادي
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)
            else:
                pdf.set_font('Arial', 'B', 13)
            pdf.set_text_color(0, 0, 0)  # أسود

            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, pdf.ar_text("إجمالي التدفقات الداخلة"), border=1, align='C')
            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, pdf.ar_text("إجمالي"), border=1, align='C')
            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, str(row_num), border=1, align='C')

            y += row_height
            row_num += 1

            # إجمالي التدفقات الخارجة
            x = 10
            pdf.set_xy(x, y)
            # مبلغ الإجمالي بأحمر غامق وخط Calibri 14 Bold
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 14)
            else:
                pdf.set_font('Arial', 'B', 14)
            pdf.set_text_color(139, 0, 0)  # أحمر غامق
            pdf.cell(col_width, row_height, f"{total_outflows:,.2f}", border=1, align='C')

            # باقي الخلايا بخط صفوف الجدول العادي
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)
            else:
                pdf.set_font('Arial', 'B', 13)
            pdf.set_text_color(0, 0, 0)  # أسود

            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, pdf.ar_text("إجمالي التدفقات الخارجة"), border=1, align='C')
            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, pdf.ar_text("إجمالي"), border=1, align='C')
            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, str(row_num), border=1, align='C')

            y += row_height
            row_num += 1

            # صافي التدفقات النقدية
            x = 10
            pdf.set_xy(x, y)

            # مبلغ النتيجة بخط Calibri 14 Bold وتلوين حسب الحالة
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 14)
            else:
                pdf.set_font('Arial', 'B', 14)

            if net_cash_flow > 0:
                pdf.set_text_color(0, 0, 139)  # أزرق غامق
            elif net_cash_flow < 0:
                pdf.set_text_color(139, 0, 0)  # أحمر غامق
            else:
                pdf.set_text_color(0, 0, 139)  # أزرق غامق

            pdf.cell(col_width, row_height, f"{net_cash_flow:,.2f}", border=1, align='C')

            # باقي الخلايا بخط صفوف الجدول العادي
            if pdf.calibri_bold_available:
                pdf.set_font('Calibri', 'B', 13)
            else:
                pdf.set_font('Arial', 'B', 13)
            pdf.set_text_color(0, 0, 0)  # أسود

            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, pdf.ar_text("صافي التدفقات النقدية"), border=1, align='C')
            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, pdf.ar_text("النتيجة"), border=1, align='C')
            x += col_width
            pdf.set_xy(x, y)
            pdf.cell(col_width, row_height, str(row_num), border=1, align='C')

            # إعادة تعيين لون النص
            pdf.set_text_color(0, 0, 0)

        except Exception as e:
            print(f"خطأ في إنشاء الجدول: {str(e)}")




def main():
    """الدالة الرئيسية"""
    import sys
    app = QApplication(sys.argv)

    # تطبيق الخط العربي
    app.setFont(QFont("Helvetica", 10))

    window = CashFlowWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
