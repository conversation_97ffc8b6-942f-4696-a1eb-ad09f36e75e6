#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة التدفقات النقدية
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cash_flow_window import CashFlowManager

def test_cash_flow_manager():
    """اختبار مدير التدفقات النقدية"""
    print("🔍 اختبار مدير التدفقات النقدية...")
    
    # إنشاء مدير التدفقات النقدية
    manager = CashFlowManager("data.db")
    
    # تحديد فترة الاختبار (آخر 30 يوم)
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    print(f"📅 فترة الاختبار: من {start_date_str} إلى {end_date_str}")
    
    # اختبار جلب التدفقات الداخلة
    print("\n💰 اختبار التدفقات الداخلة:")
    inflows = manager.get_cash_inflows(start_date_str, end_date_str)
    print(f"عدد التدفقات الداخلة: {len(inflows)}")
    
    if inflows:
        print("أول 3 تدفقات داخلة:")
        for i, inflow in enumerate(inflows[:3]):
            print(f"  {i+1}. التاريخ: {inflow['date']}, المبلغ: {inflow['amount']}, المصدر: {inflow['source']}")
    
    # اختبار جلب التدفقات الخارجة
    print("\n💸 اختبار التدفقات الخارجة:")
    outflows = manager.get_cash_outflows(start_date_str, end_date_str)
    print(f"عدد التدفقات الخارجة: {len(outflows)}")
    
    if outflows:
        print("أول 3 تدفقات خارجة:")
        for i, outflow in enumerate(outflows[:3]):
            print(f"  {i+1}. التاريخ: {outflow['date']}, المبلغ: {outflow['amount']}, النوع: {outflow['type']}")
    
    # حساب الملخص
    total_inflows = sum(inflow['amount'] for inflow in inflows)
    total_outflows = sum(outflow['amount'] for outflow in outflows)
    net_flow = total_inflows - total_outflows
    
    print(f"\n📊 ملخص التدفقات النقدية:")
    print(f"إجمالي التدفقات الداخلة: {total_inflows:,.2f} درهم")
    print(f"إجمالي التدفقات الخارجة: {total_outflows:,.2f} درهم")
    print(f"صافي التدفق النقدي: {net_flow:,.2f} درهم")
    
    if net_flow > 0:
        print("✅ التدفق النقدي إيجابي (ربح)")
    elif net_flow < 0:
        print("⚠️ التدفق النقدي سلبي (خسارة)")
    else:
        print("⚖️ التدفق النقدي متوازن")

if __name__ == "__main__":
    test_cash_flow_manager()
