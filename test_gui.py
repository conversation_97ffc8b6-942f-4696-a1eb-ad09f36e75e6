#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار واجهة التدفقات النقدية
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cash_flow_window import CashFlowWindow

def main():
    """الدالة الرئيسية لاختبار الواجهة"""
    app = QApplication(sys.argv)
    
    # إنشاء النافذة
    window = CashFlowWindow()
    window.show()
    
    # إغلاق النافذة تلقائياً بعد 5 ثوان للاختبار
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(5000)  # 5 ثوان
    
    print("🔍 تم فتح نافذة التدفقات النقدية...")
    print("سيتم إغلاق النافذة تلقائياً بعد 5 ثوان...")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
